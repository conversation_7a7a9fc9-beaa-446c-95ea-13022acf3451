package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class PointOnSectorWithRatioTransformData @BsonCreator constructor(
    @BsonProperty("targetParamIdx") val targetParamIdx: Int,
    @BsonProperty("paramKind") val paramKind: String,
    @BsonProperty("center") val center: DoubleArray,
    @BsonProperty("pS") val pS: DoubleArray,
    @BsonProperty("pE") val pE: DoubleArray,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}