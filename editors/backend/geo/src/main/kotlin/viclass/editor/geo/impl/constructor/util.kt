package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.numbers.core.Precision
import viclass.editor.geo.constructor.ConstructionParams
import viclass.editor.geo.constructor.ExtractionResult
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.ExtractionNotFoundException
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.PI
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin

const val DEFAULT_TOLERANCE = 1.0e-8
val DEFAULT_PRECISION = Precision.doubleEquivalenceOfEpsilon(DEFAULT_TOLERANCE)

/**
 * An util function to find the first possible extractor that satisfies among the candidate extractors
 * Record down the extractor id used inside the param specs
 */
@Throws(RuntimeException::class)
fun <T : ExtractionResult<out Any>> extractFirstPossible(
    doc: GeoDoc,
    extractionName: String,
    constraint: ConstructionParams,
    ctIdx: Int,
): T {
    var usedExtractor: String? = null
    // an extractor has been used before and recorded in specs
    if (constraint.specs.extractorIds.containsKey(extractionName)) {
        usedExtractor = constraint.specs.extractorIds[extractionName]
    }

    for (extr in constraint.extractor(extractionName)) {
        if (usedExtractor != null && extr.id != usedExtractor) continue

        val extractedResult = extr.extract<T>(doc, extractionName, constraint.paramDef.id, constraint.specs, ctIdx)
        constraint.useExtractor(extractionName, extr)

        return extractedResult
    }

    throw ExtractionNotFoundException("No extractor was found to be suitable for extracting $extractionName")
}

fun radian(degree: Double): Double {
    return degree * PI / 180
}

fun degree(radian: Double): Double {
    return radian * 180 / PI
}

/**
 * Returns new point rotated by given angle around given center point.
 * If center point is omitted, rotates around zero point (0,0).
 * Positive value of angle defines rotation in counterclockwise direction,
 * negative angle defines rotation in clockwise direction
 * @param {number} angle - angle in radians
 * @param {Point} [center=(0,0)] center
 * @returns {Point}
 */
fun Vector3D.rotate(angleRadian: Double, center: Vector3D = Vector3D.ZERO): Vector3D {
    val x_rot = center.x + (this.x - center.x) * cos(angleRadian) - (this.y - center.y) * sin(angleRadian)
    val y_rot = center.y + (this.x - center.x) * sin(angleRadian) + (this.y - center.y) * cos(angleRadian)

    return Vector3D.of(x_rot, y_rot, 0.0)
}

/**
 * Return angle between this vector and other vector. <br/>
 * Angle is measured from 0 to 2*PI in the counterclockwise direction
 * from current vector to another.
 * @param {Vector} v Another vector
 * @returns {number}
 */
fun Vector3D.angleTo(v: Vector3D): Double {
    val norm1 = this.normalize()
    val norm2 = v.normalize()
    var angle = atan2(norm1.crossProduct(norm2), norm1.dot(norm2))
    if (angle < 0) angle += 2 * Math.PI;
    return angle
}

/**
 * Returns vector product (cross product) of two vectors <br/>
 * <code>cross_product = (this x v)</code>
 * @param {Vector} v Other vector
 * @returns {number}
 */
fun Vector3D.crossProduct(v: Vector3D): Double {
    return (this.x * v.y - this.y * v.x);
}

fun Vector3D.toLineVi(doc: GeoDoc, name: String?, p1: Point, p2: Point? = null): LineVi {
    return LineImpl(doc, name, p1, this, p2)
}

fun Vector3D.toPoint(doc: GeoDoc, name: String?): Point {
    return PointImpl(doc, name, this.x, this.y, this.z)
}

fun Vector3D.rotate90Degrees(): Vector3D {
    return Vector3D.of(-this.y, this.x, this.z)
}
