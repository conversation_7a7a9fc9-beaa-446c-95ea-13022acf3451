{"extends": "../../../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "module": "commonjs", "sourceMap": true, "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "exclude": ["src/test.ts", "**/*.spec.ts"]}