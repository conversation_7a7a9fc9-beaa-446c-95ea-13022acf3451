import { DocLocalId, DocumentId, EditorBase, EntityRegistry, VDocCtrl, ViewportId } from '../api';

export class RegistryDelegator<TDocCtrl extends VDocCtrl> {
    constructor(private editor: EditorBase<TDocCtrl>) {}

    getDocReg(vpId: ViewportId): EntityRegistry<TDocCtrl> | undefined {
        return this.editor.regMan.registry<TDocCtrl>(this.editor.docReg(vpId));
    }

    getOrCreateDocReg(vpId: ViewportId): EntityRegistry<TDocCtrl> {
        return this.editor.regMan.register<TDocCtrl>(this.editor.docReg(vpId));
    }

    hasDocReg(vpId: ViewportId): boolean {
        return this.editor.regMan.has(this.editor.docReg(vpId));
    }

    deleteDocReg(vpId: ViewportId): EntityRegistry<TDocCtrl> | undefined {
        return this.editor.regMan.delete<TDocCtrl>(this.editor.docReg(vpId));
    }

    getLayerReg(vpId: ViewportId, docId: DocLocalId): EntityRegistry<any> | undefined {
        return this.editor.regMan.registry<any>(this.editor.layerReg(vpId, docId));
    }

    getOrCreateLayerReg(vpId: ViewportId, docId: DocLocalId): EntityRegistry<any> {
        return this.editor.regMan.register<any>(this.editor.layerReg(vpId, docId));
    }

    hasLayerReg(vpId: ViewportId, docId: DocLocalId): boolean {
        return this.editor.regMan.has(this.editor.layerReg(vpId, docId));
    }

    deleteLayerReg(vpId: ViewportId, docId: DocLocalId): EntityRegistry<any> | undefined {
        return this.editor.regMan.delete<any>(this.editor.layerReg(vpId, docId));
    }

    findDocumentByLocalId(vpId: ViewportId, docLocalId: DocLocalId): TDocCtrl | undefined {
        const reg = this.editor.regMan.registry<TDocCtrl>(this.editor.docReg(vpId));
        return reg?.hasEntityId(docLocalId) ? reg.getEntity(docLocalId) : undefined;
    }

    findDocumentByGlobalId(vpId: ViewportId, globalId: DocumentId): TDocCtrl | undefined {
        return this.findAllDocuments(vpId).find(doc => doc.state.globalId === globalId);
    }

    findAllDocuments(vpId: ViewportId): TDocCtrl[] {
        const reg = this.editor.regMan.registry<TDocCtrl>(this.editor.docReg(vpId));
        return reg ? reg.allEntities() : [];
    }
}
