import { Subject } from 'rxjs';
import { concatMap } from 'rxjs/operators';
import { CompletableData, VEventData, VEventListener, VEventSource } from './api';

export class DefaultEventEmitter<T extends VEventData<any, any, any>> implements VEventSource<T> {
    listeners: VEventListener<T>[] = [];

    source: Subject<CompletableData<any>>;

    constructor() {
        this.source = new Subject<any>();
        this.source
            .pipe(
                concatMap(v => {
                    const arr: Promise<any>[] = [];
                    for (const l of this.listeners) {
                        const res = l.onEvent(v.data as T);
                        if (res instanceof Promise) {
                            arr.push(res);
                        }
                    }
                    return Promise.all(arr).then(
                        () => v.complete(true),
                        err => v.error(err)
                    );
                })
            )
            .subscribe({
                error: e => console.error('Emit event error', e),
                complete: () => console.warn('Event emiter completed', this),
            });
    }

    emit(event: T): Promise<any> {
        const listableEvent = new CompletableData(event);
        this.source.next(listableEvent);
        return listableEvent.promise;
    }

    /**
     * Sending an event to all listeners without queueing them in rxjs.
     * For normal cases, please use `emit()` instead.
     */
    emitSync(event: T): Promise<any[]> | null {
        const promisses: Promise<any>[] = [];

        for (const l of this.listeners) {
            const res = l.onEvent(event);
            if (res instanceof Promise) {
                promisses.push(res);
            }
        }

        return promisses.length ? Promise.all(promisses) : null;
    }

    /**
     * User of the default event emitter might want cache the event before emitting sometimes in future
     * and need to access the Promise before hand. In this case, the CompletableData can be provided instead
     * of the original event and the event emitter will complete the CompletableData when it has emitted to all
     * listeners completely.
     * @param data
     * @returns
     */
    emitCompletable(data: CompletableData<T>): Promise<any> {
        this.source.next(data);
        return data.promise;
    }

    registerListener(listener: VEventListener<T>) {
        if (this.onBeforeListenerAdded) this.onBeforeListenerAdded(listener);
        if (!this.listeners.includes(listener)) this.listeners.push(listener);
        if (this.onAfterListenerAdded) this.onAfterListenerAdded(listener);
    }

    unregisterListener(listener: VEventListener<T>) {
        const i = this.listeners.findIndex(v => v == listener);
        if (i >= 0) {
            if (this.onBeforeListenerRemoved) this.onBeforeListenerRemoved(listener);
            this.listeners.splice(i, 1);
            if (listener.onUnregister) listener.onUnregister();
            if (this.onAfterListenerRemoved) this.onAfterListenerRemoved(listener);
        }
    }

    clearListeners() {
        this.listeners.forEach(l => this.unregisterListener(l));
    }

    hasHandler(): boolean {
        return this.listeners.length > 0;
    }

    onBeforeListenerAdded?: (listener: VEventListener<T>) => void;
    onBeforeListenerRemoved?: (listener: VEventListener<T>) => void;
    onAfterListenerAdded?: (listener: VEventListener<T>) => void;
    onAfterListenerRemoved?: (listener: VEventListener<T>) => void;
}
