import { EqRelation, MathJsonExpr } from '../types';

export const RELATION_OPRS = new Set([
    'Equal',
    'Less',
    'Greater',
    'LessEqual',
    'GreaterEqual',
    'NotEqual',
    'Congruent',
]);

export const ERROR_OPRS = new Set(['Error']);

export const VAR_REGEX = /^[A-Za-z]+$/;

export interface EquationAnalyzer {
    analyze(): MathJsonExpr;
    reset(): void;
}

export function getEquationRelation(rel: any): EqRelation {
    if (typeof rel !== 'string') {
        return EqRelation.Invalid;
    }

    switch (rel) {
        case 'Equal':
            return EqRelation.Equal;
        case 'Greater':
            return EqRelation.Greater;
        case 'GreaterEqual':
            return EqRelation.GreaterEqual;
        case 'Less':
            return EqRelation.Less;
        case 'LessEqual':
            return EqRelation.LessEqual;
        case 'NotEqual':
        case 'Congruent':
            return EqRelation.Invalid;
        default:
            return EqRelation.Equal;
    }
}
