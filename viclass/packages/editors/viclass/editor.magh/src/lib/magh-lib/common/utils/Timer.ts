export class Timer {
    private _startTime: number;
    private _lastRecordTime: number;
    private _totalElapsed: number;

    constructor() {
        this._startTime = Date.now();
        this._lastRecordTime = this._startTime;
        this._totalElapsed = 0;
    }

    public reset(): void {
        this._startTime = Date.now();
        this._lastRecordTime = this._startTime;
        this._totalElapsed = 0;
    }

    public record(): number {
        const currentTime = Date.now();
        const elapsedSinceLastRecord = currentTime - this._lastRecordTime;
        this._totalElapsed += elapsedSinceLastRecord;
        this._lastRecordTime = currentTime;
        return elapsedSinceLastRecord;
    }

    public get elapse(): number {
        return Date.now() - this._lastRecordTime;
    }

    public get totalElapsed(): number {
        return this._totalElapsed;
    }
}
