import {
    BaseBoardViewportManager,
    BoardViewportManager,
    Cmd,
    CmdChannel,
    CmdCombinator,
    cmdMeta,
    InferredPointerEvent,
    mouseLocation,
    newCursor,
    NumDPointerChange,
    Position,
    reliableSaveCmdMeta,
    UIPointerEventData,
    VEventListener,
    ViewportManager,
    ZoomEventData,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.freedrawing';

import { InsertPartialPencilObjCmd, StartPreviewCmd, UpdatePreviewCmd } from '../cmd/freedrawing.cmd';
import { FreedrawingEditor, FreedrawingToolBar, FreedrawingToolEventData } from '../freedrawing.api';
import { CommonToolState, FreedrawingHistoryItem, PenObj } from '../freedrawing.models';
import { convertProtoToPosition, freedrawingObjectReg } from '../freedrawing.util';
import { FreedrawingObjCtrl } from '../objects/freedrawing.obj.ctrl';
import { PenObjCtrl } from '../objects/freedrawing.pen.obj.ctrl';
import { FreedrawingTool } from './freedrawing.tool';

const MOUSE_TIME_THROTTLE = 10;

/**
 *
 * <AUTHOR>
 */

export class PenTool extends FreedrawingTool<CommonToolState, PenObjCtrl> {
    readonly toolType = 'PencilTool';

    private cmdCombinator: CmdCombinator;
    private curPreviewObjId: number;

    private pointerPositions: UIPointerEventData<any>[] = [];
    // list of stroke to be pushed to the UI.
    // an array (order of stroke is important)
    // of tuple of preview obj id and its pending pointer points
    private pendingStrokes: [number, UIPointerEventData<any>[], CmdCombinator][] = [];
    private hasPointerDown: boolean = false;
    private requestedFrame: boolean;
    private previewEnded: Map<number, boolean> = new Map<number, boolean>(); // map from preview obj id to whether the preview has ended or not

    private viewportZoomListener = new (class implements VEventListener<ZoomEventData> {
        constructor(private t: PenTool) {}
        onEvent(eventData: any): any {
            switch (eventData.eventType) {
                case 'viewport-zoom': {
                    this.t.updateCursorWithZoom();
                    break;
                }
            }
            return eventData;
        }
    })(this);
    lastPointerMoveTime: number;

    constructor(
        protected override editor: FreedrawingEditor,
        override readonly toolbar: FreedrawingToolBar
    ) {
        super(editor, toolbar);

        this.updateCursor([
            newCursor('cursor_brush-default'),
            newCursor('vcon_cursor_brush', this.toolState?.lineWidth, this.toolState.stroke, 0, 'icon'),
        ]);
    }

    get toolState(): CommonToolState {
        return this.toolbar.toolState('CommonPropertiesTool');
    }

    override handleNonUIPointerEvent(
        event: NumDPointerChange | InferredPointerEvent
    ): NumDPointerChange | InferredPointerEvent {
        if (event.eventType == 'numdpointer') {
            // if the number of total > 1, we should end all current preview if any
            if (event.totalPointer > 1) {
                this.finishPreviewIfAny(event.viewport);
            }
        }

        return event;
    }

    // create a new preview object
    // and set it as the current preview object
    override async onPointerDown(event: UIPointerEventData<any>): Promise<boolean> {
        if (this.needAsyncCreation(event.viewport)) {
            await this.doCreateAsync(event.viewport);
        }

        const docCtrl = this.curDoc(event.viewport);
        if (!docCtrl) return false;

        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(event.viewport.id, docCtrl.state.id)
        );
        this.curPreviewObjId = objectRegistry.getAndIncrementId();

        // create new preview object
        const pointerdownPos = mouseLocation(event);
        const obj: PenObj = {
            id: this.curPreviewObjId,
            toolType: this.toolType,
            toolState: this.toolState,
            points: [pointerdownPos],
        };

        const layer = this.topLayer(docCtrl);

        // and start preview
        const meta = cmdMeta(event.viewport, obj.id, CmdTypeProto.START_PREVIEW);
        meta.versionable = docCtrl.state.id;

        const startCmd = new StartPreviewCmd(meta);
        startCmd.setState(this.toolType, obj, layer?.state?.id || 1);
        this.sendCommand(startCmd);

        this.cmdCombinator = PenTool.CmdCombinatorImpl(this, event.viewport, obj.id);
        this.cmdCombinator.combine(startCmd);

        this.hasPointerDown = true;

        return true;
    }

    override onPointerMove(event: UIPointerEventData<any>): boolean {
        const now = new Date();

        if (now.getTime() - this.lastPointerMoveTime < MOUSE_TIME_THROTTLE)
            return false; // throttle the mouse move event)
        else this.lastPointerMoveTime = now.getTime();

        if (!this.hasPointerDown || !super.onPointerMove(event)) return false; // all move event in between a pointer up and pointer down is ignored

        this.pointerPositions.push(event);
        this.processingFrame();

        return true;
    }

    private processingFrame() {
        if (!this.requestedFrame) {
            this.requestedFrame = true;
            requestAnimationFrame(() => {
                this.requestedFrame = false;
                // if there are pending stroke
                if (this.pendingStrokes.length > 0) {
                    for (const stroke of this.pendingStrokes) {
                        this.updatePreview(stroke[0], stroke[2], stroke[1], true);
                        this.previewEnded.delete(stroke[0]); // clean up ended
                    }

                    this.pendingStrokes = [];
                }
                // if there are pointer positions
                const lastPoint = this.previewEnded.get(this.curPreviewObjId) === true;
                this.updatePreview(this.curPreviewObjId, this.cmdCombinator, this.pointerPositions, lastPoint);

                if (lastPoint) {
                    this.previewEnded.delete(this.curPreviewObjId);
                    this.cmdCombinator = undefined;
                }

                this.pointerPositions = [];
            });
        }
    }

    protected override hasPreviewObjectCtrl(vm: BoardViewportManager): boolean {
        const docCtrl = this.curDoc(vm);
        if (!docCtrl) return false;

        const layer = this.topLayerRenderer(docCtrl);
        if (!layer) return false;

        return layer.hasObjectId(this.curPreviewObjId);
    }

    mouseLocation(event: PointerEvent): Position[] {
        const vp = this.toolbar.viewport as BoardViewportManager;
        const boundingRect = (event.target as Element).getBoundingClientRect();
        const result = [];

        for (const e of event.getCoalescedEvents()) {
            result.push(
                vp.getBoardPos({
                    x: e.clientX - boundingRect.x,
                    y: e.clientY - boundingRect.y,
                })
            );
        }

        result.push(
            vp.getBoardPos({
                x: event.clientX - boundingRect.x,
                y: event.clientY - boundingRect.y,
            })
        );

        // because the unbounded layer cover exactly the same area as the viewportRoot element, we just simply using
        // the method from the viewport
        return result;
    }

    private updatePreview(
        previewObjId: number,
        cmdCombinator: CmdCombinator,
        event: UIPointerEventData<any>[],
        endPoint: boolean
    ) {
        if (event.length == 0) {
            if (endPoint) this.finishPreviewIfAny(this.toolbar.viewport); // if this is the last update, we send end preview
            return;
        }

        const vp = event[0].viewport;
        const docCtrl = this.curDoc(vp);
        if (!docCtrl) return;

        // NOTE: if there are still points to be sent, we need to finish preview only when they have been combined,
        // hence, the end preview is called from the combinator

        for (let i = 0; i < event.length; i++) {
            const e = event[i];
            const pos = this.mouseLocation(e.nativeEvent);

            const layer = this.topLayer(docCtrl);

            for (let j = 0; j < pos.length; j++) {
                const meta = cmdMeta(vp, previewObjId, CmdTypeProto.UPDATE_POSITION_PREVIEW);
                meta.versionable = docCtrl.state.id;
                const end = i == event.length - 1 && j == pos.length - 1;
                const cmd = new UpdatePreviewCmd(meta);
                cmd.updatePosition(this.toolType, pos[j], end ? endPoint : false, layer.state.id);
                this.sendCommand(cmd);

                cmdCombinator.combine(cmd);
            }
        }
    }

    /**
     * Process pointer up event
     * Put the ending position to the end of the current pointer position
     * Push the list of pointer position to the list of pending stroke
     * Request a processing frame.
     * @param event
     * @returns
     */
    override onPointerUp(event: UIPointerEventData<any>): boolean {
        this.pointerPositions.push(event);
        this.hasPointerDown = false; // no longer has pointerdown
        this.pendingStrokes.push([this.curPreviewObjId, this.pointerPositions, this.cmdCombinator]);
        this.previewEnded.set(this.curPreviewObjId, true); // mark the preview as ended
        this.processingFrame();

        return true;
    }

    private static CmdCombinatorImpl(tool: PenTool, vm: ViewportManager, previewId: number): CmdCombinator {
        return new (class implements CmdCombinator {
            isCombining: boolean = false;
            cmdType: number;
            viewport: string;

            timeThreshold = 500;
            counterThreshold = 100;

            buffer: Position[] = [];
            timeCheckerId: any;
            isCompleted: boolean = false;

            startSequence(sender: (cmd: Cmd<any>) => void) {
                throw new Error('Method not implemented.');
            }

            combine(cmd: Cmd<any>) {
                if (cmd.meta.cmdType == CmdTypeProto.START_PREVIEW) {
                    const startCmd = cmd as StartPreviewCmd;
                    this.buffer = startCmd.state
                        .getPencil()
                        .getPointsList()
                        .map(p => convertProtoToPosition(p));

                    this.timeCheckerId = setTimeout(() => {
                        this.timeChecker();
                    }, this.timeThreshold);
                }

                if (cmd.meta.cmdType == CmdTypeProto.UPDATE_POSITION_PREVIEW) {
                    const updateCmd = cmd as UpdatePreviewCmd;
                    this.buffer.push(convertProtoToPosition(updateCmd.state.getPoint()));

                    if (updateCmd.state.getEndPoint() == true) {
                        this.flushBuffer();
                        tool.finishPreviewIfAny(vm);
                    } else {
                        this.counterChecker();
                    }
                }
            }

            private timeChecker() {
                if (this.isCombining || this.isCompleted) return;
                this.isCombining = true;

                // send all remaining
                const points = this.buffer;
                this.buffer = [];
                this.createAndSendCmd(points);

                this.isCombining = false;

                // schedule new time checker
                if (!this.isCompleted) {
                    this.timeCheckerId = setTimeout(() => this.timeChecker(), this.timeThreshold);
                }
            }

            private createAndSendCmd(points: Position[]) {
                if (points.length == 0) return;

                const obj: PenObj = {
                    id: previewId,
                    toolState: tool.toolState,
                    toolType: tool.toolType,
                    points: points,
                };

                const docCtrl = tool.curDoc(vm);
                const layer = tool.topLayer(docCtrl);

                const meta = reliableSaveCmdMeta(
                    vm,
                    docCtrl.state,
                    docCtrl.state.id,
                    previewId,
                    CmdTypeProto.INSERT_PARTIAL_PENCIL_OBJ
                );
                const cmd = new InsertPartialPencilObjCmd(meta);
                cmd.setState(obj, layer.state.id);

                tool.sendCommand(cmd);

                // create history item
                const state: PenObj = JSON.parse(JSON.stringify(obj));

                const item: FreedrawingHistoryItem = {
                    supporter: tool.editor,
                    viewportId: vm.id,
                    docId: docCtrl.state.id,
                    layerId: layer.state.id,
                    objId: obj.id,
                    toolType: tool.toolType,
                    state: state,
                    action: 'INSERT_PARTIAL',
                    firstObj: tool.firstObj,
                    lastPartial: this.isCompleted,
                };
                tool.editor.addHistoryItem(item);

                if (this.isCompleted) {
                    if (tool.firstObj) {
                        tool.firstObj = false;
                    }
                }
            }

            private flushBuffer() {
                this.isCompleted = true;

                if (this.isCombining) return;
                this.isCombining = true;

                // clear time checker scheduler
                if (this.timeCheckerId) clearTimeout(this.timeCheckerId);

                // send all remaining
                const points = this.buffer;
                this.buffer = [];
                this.createAndSendCmd(points);

                this.isCombining = false;
            }

            private counterChecker() {
                if (this.buffer.length < this.counterThreshold || this.isCombining) return;
                this.isCombining = true;

                // clear time checker scheduler
                if (this.timeCheckerId) clearTimeout(this.timeCheckerId);

                // update curve
                const points = this.buffer.splice(0, this.counterThreshold);
                this.createAndSendCmd(points);

                this.isCombining = false;
                // schedule new time threshole checker
                this.timeCheckerId = setTimeout(() => this.timeChecker(), this.timeThreshold);
            }

            setChannel(cmdChannel: CmdChannel) {
                throw new Error('Method not implemented.');
            }

            endSequenceAndFlush(): Promise<void> {
                throw new Error('Method not implemented.');
            }
        })();
    }

    override onAttachViewport() {
        (this.toolbar.viewport as BoardViewportManager)
            .zoomEventEmitter()
            .registerListener(this.viewportZoomListener as VEventListener<ZoomEventData>);
    }

    override onDetachViewport() {
        (this.toolbar.viewport as BoardViewportManager)
            .zoomEventEmitter()
            .unregisterListener(this.viewportZoomListener as VEventListener<ZoomEventData>);
    }

    protected override onToolChange(event: FreedrawingToolEventData, vm: ViewportManager): void {
        super.onToolChange(event, vm);
        this.updateCursorWithZoom();
    }

    private updateCursorWithZoom() {
        const zoomLevel = (this.toolbar.viewport as BaseBoardViewportManager)?.zoomLevel || 1;
        this.updateCursor([
            newCursor('cursor_brush-default', 37 / zoomLevel),
            newCursor('vcon_cursor_brush', this.toolState?.lineWidth / zoomLevel, this.toolState.stroke, 0, 'icon'),
        ]);
    }
}
