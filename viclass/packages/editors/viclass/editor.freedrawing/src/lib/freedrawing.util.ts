import { DefaultVDocCtrl, <PERSON>sition, Rectangle } from '@viclass/editor.core';
import {
    BoundaryProto,
    CommonToolStateProto,
    EraserObjectProto,
    EraserToolStateProto,
    LineObjectV2Proto,
    LineV2ToolStateProto,
    PencilObjectProto,
    PositionProto,
    ShapeObjectProto,
} from '@viclass/proto/editor.freedrawing';
import { FreedrawingEditor } from './freedrawing.editor';
import {
    CommonToolState,
    EraserObj,
    EraserToolState,
    FreedrawingToolType,
    LineObjV2,
    LineV2ToolState,
    PenObj,
    ShapeObj,
} from './freedrawing.models';
import { EraserObjCtrl } from './objects/freedrawing.eraser.obj.ctrl';
import { LineObjV2Ctrl } from './objects/freedrawing.line.v2.obj.ctrl';
import { PenObjCtrl } from './objects/freedrawing.pen.obj.ctrl';
import {
    HexagonObjCtrl,
    LineObjCtrl,
    OvalObjCtrl,
    RectangleObjCtrl,
    ShapeObjCtrl,
    TriangleObjCtrl,
} from './objects/freedrawing.shape.obj.ctrl';

export function convertProtoToBoundary(boundary: BoundaryProto): Rectangle {
    return {
        start: { x: boundary.getStart().getX(), y: boundary.getStart().getY() },
        end: { x: boundary.getEnd().getX(), y: boundary.getEnd().getY() },
    };
}

export function convertProtoToCommonToolState(proto: CommonToolStateProto): CommonToolState {
    const state = {
        stroke: proto['hasStrokeColor']() ? proto.getStrokeColor() : undefined,
        fill: proto['hasFillColor']() ? proto.getFillColor() : undefined,
        lineWidth: proto.getLineWidth(),
    };

    return state;
}

export function convertProtoToEraserToolState(proto: EraserToolStateProto): EraserToolState {
    const state = {
        size: proto.getSize(),
    };

    return state;
}

export function convertProtoToPosition(proto: PositionProto): Position {
    return { x: proto.getX(), y: proto.getY() };
}

export function buildEraserToolStateProto(toolState: EraserToolState): EraserToolStateProto {
    return new EraserToolStateProto().setSize(toolState.size);
}

export function buildBoundaryProto(boundary: Rectangle): BoundaryProto {
    const proto = new BoundaryProto()
        .setStart(new PositionProto().setX(boundary.start.x).setY(boundary.start.y))
        .setEnd(new PositionProto().setX(boundary.end.x).setY(boundary.end.y));

    return proto;
}

export function buildShapeObjProto(obj: ShapeObj): ShapeObjectProto {
    const proto = new ShapeObjectProto()
        .setLocalId(obj.id)
        .setToolType(obj.toolType)
        .setBoundary(buildBoundaryProto(obj.boundary))
        .setToolState(buildCommonToolStateProto(obj.toolState));

    return proto;
}

export function buildPencilObjProto(obj: PenObj): PencilObjectProto {
    const proto = new PencilObjectProto()
        .setLocalId(obj.id)
        .setPointsList(obj.points.map(p => buildPositionProto(p)))
        .setToolState(buildCommonToolStateProto(obj.toolState));

    return proto;
}

export function buildEraserObjProto(obj: EraserObj): EraserObjectProto {
    const proto = new EraserObjectProto()
        .setLocalId(obj.id)
        .setPointsList(obj.points.map(p => buildPositionProto(p)))
        .setToolState(buildEraserToolStateProto(obj.toolState));

    return proto;
}

export function buildLineObjV2Proto(obj: LineObjV2): LineObjectV2Proto {
    const proto = new LineObjectV2Proto()
        .setLocalId(obj.id)
        .setBoundary(buildBoundaryProto(obj.boundary))
        .setToolState(buildLineObjV2ToolStateProto(obj.toolState));

    return proto;
}

export function buildLineObjV2ToolStateProto(toolState: LineV2ToolState): LineV2ToolStateProto {
    const proto = new LineV2ToolStateProto().setLineWidth(toolState.lineWidth);
    if (typeof toolState.startArrow === 'boolean') {
        proto.setStartArrow(toolState.startArrow);
    }
    if (typeof toolState.endArrow === 'boolean') {
        proto.setEndArrow(toolState.endArrow);
    }
    if (toolState.stroke) {
        proto.setStrokeColor(toolState.stroke);
    }
    return proto;
}

export function convertProtoToLineObjV2ToolState(proto: LineV2ToolStateProto): LineV2ToolState {
    return {
        stroke: proto.hasStrokeColor() ? proto.getStrokeColor() : '#000000',
        lineWidth: proto.getLineWidth(),
        startArrow: proto.hasStartArrow() ? proto.getStartArrow() : false,
        endArrow: proto.hasEndArrow() ? proto.getEndArrow() : false,
    };
}

export function buildPositionProto(point: Position): PositionProto {
    return new PositionProto().setX(point.x).setY(point.y);
}

export function buildCommonToolStateProto(toolState: CommonToolState): CommonToolStateProto {
    const proto = new CommonToolStateProto().setLineWidth(toolState.lineWidth);

    if (toolState.fill) proto.setFillColor(toolState.fill);
    if (toolState.stroke) proto.setStrokeColor(toolState.stroke);

    return proto;
}

export function convertProtoToShapeObjCtrl(
    proto: ShapeObjectProto,
    editor: FreedrawingEditor,
    docCtrl: DefaultVDocCtrl
): ShapeObjCtrl {
    const obj: ShapeObj = {
        id: proto.getLocalId(),
        boundary: convertProtoToBoundary(proto.getBoundary()),
        toolType: proto.getToolType() as FreedrawingToolType,
        toolState: convertProtoToCommonToolState(proto.getToolState()),
    };

    switch (proto.getToolType()) {
        case 'LineTool': {
            return new LineObjCtrl(obj, editor, docCtrl, false);
        }
        case 'OvalTool': {
            return new OvalObjCtrl(obj, editor, docCtrl, false);
        }
        case 'HexagonTool': {
            return new HexagonObjCtrl(obj, editor, docCtrl, false);
        }
        case 'RectangleTool': {
            return new RectangleObjCtrl(obj, editor, docCtrl, false);
        }
        case 'TriangleTool': {
            return new TriangleObjCtrl(obj, editor, docCtrl, false);
        }
        default:
            throw new Error(`shape type ${proto.getToolType()} is not valid`);
    }
}

export function convertProtoToPencilObjCtrl(
    proto: PencilObjectProto,
    editor: FreedrawingEditor,
    docCtrl: DefaultVDocCtrl
): PenObjCtrl {
    const obj: PenObj = {
        id: proto.getLocalId(),
        points: proto.getPointsList().map(p => convertProtoToPosition(p)),
        toolType: 'PencilTool',
        toolState: convertProtoToCommonToolState(proto.getToolState()),
    };

    return new PenObjCtrl(obj, editor, docCtrl);
}

export function convertProtoToEraserObjCtrl(
    proto: EraserObjectProto,
    editor: FreedrawingEditor,
    docCtrl: DefaultVDocCtrl
): EraserObjCtrl {
    const obj: EraserObj = {
        id: proto.getLocalId(),
        points: proto.getPointsList().map(p => convertProtoToPosition(p)),
        toolType: 'EraserTool',
        toolState: convertProtoToEraserToolState(proto.getToolState()),
    };

    return new EraserObjCtrl(obj, editor, docCtrl);
}

export function convertProtoToLineObjV2Ctrl(
    proto: LineObjectV2Proto,
    editor: FreedrawingEditor,
    docCtrl: DefaultVDocCtrl
): LineObjV2Ctrl {
    const obj = new LineObjV2(proto.getLocalId());
    obj.boundary = convertProtoToBoundary(proto.getBoundary());
    obj.toolState = convertProtoToLineObjV2ToolState(proto.getToolState());

    return new LineObjV2Ctrl(obj, editor, docCtrl, false);
}

export function freedrawingDocReg(coordStateId: string) {
    return `freedrawingEditor/frdDoc/${coordStateId}`;
}

export function freedrawingLayerReg(coordinatorId: string, docId: number) {
    return `freedrawingEditor/frdLayer/${coordinatorId}/${docId}`;
}

export function freedrawingObjectReg(coordinatorId: string, docId: number) {
    return `freedrawingEditor/frdObject/${coordinatorId}/${docId}`;
}
