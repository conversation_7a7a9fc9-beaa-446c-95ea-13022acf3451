import { createEmptyHistoryState, HistoryState, registerHistory } from '@lexical/history';
import { mergeRegister } from '@lexical/utils';
import { HistoryFeature } from '@viclass/editor.core';
import {
    $getRoot,
    $isParagraphNode,
    CAN_REDO_COMMAND,
    CAN_UNDO_COMMAND,
    COMMAND_PRIORITY_LOW,
    REDO_COMMAND,
    UNDO_COMMAND,
} from 'lexical';
import * as Y from 'yjs';
import { WordDocCtrl } from '../../../docs/word.doc.ctrl';
import { WordHistoryItem } from '../../../history';
import { WordPlugin } from '../word.plugin';

/**
 * ! From the Yjs internal type @see UndoManager.js
 */
export type StackItemEvent = {
    stackItem: any;
    origin: any;
    type: 'undo' | 'redo';
};

/**
 * Override the undo and redo command of the default @lexical/history plugin
 * for yjs collaboration
 */
export class YHistoryPlugin extends WordPlugin {
    private historyState: HistoryState;
    private skipNextInitItem = false;
    private lastUndoTime: number | undefined = undefined;

    get undoManager(): Y.UndoManager {
        return this.wordLib.undoManager;
    }

    get historyFeature(): HistoryFeature {
        return this.wordEditor.historyFeature;
    }

    get viewportId(): string {
        return this.docCtrl.viewport.id;
    }

    constructor(docEditor: WordDocCtrl) {
        super(docEditor);
        this.historyState = createEmptyHistoryState();
    }

    override init(): void {
        this.undoManager.captureTransaction = this.captureUndoManagerTransaction;

        this.addUnsubscribe(
            mergeRegister(
                this.lexical.registerCommand(
                    UNDO_COMMAND,
                    () => {
                        const historyManager = this.historyFeature?.getHistoryManager(this.viewportId);
                        historyManager?.undo();

                        return true;
                    },
                    COMMAND_PRIORITY_LOW
                ),
                this.lexical.registerCommand(
                    REDO_COMMAND,
                    () => {
                        const historyManager = this.historyFeature?.getHistoryManager(this.viewportId);
                        historyManager?.redo();

                        return true;
                    },
                    COMMAND_PRIORITY_LOW
                ),
                registerHistory(this.lexical, this.historyState, 300)
            )
        );

        this.undoManager.on('stack-item-added', this.onStackItemAdded);
        this.undoManager.on('stack-item-popped', this.onStackItemPopped);
        this.undoManager.on('stack-cleared', this.onStackClear);
    }

    /**
     * Check if the transaction should be captured by the undo manager.
     * Currently we ignore the transaction of changing the local content of SubViewportNode.
     * As it's history will be handle by the sub-editor itself
     *
     * @param transaction Yjs transaction
     * @returns true if transaction should be captured, false otherwise
     */
    private captureUndoManagerTransaction = (transaction: Y.Transaction): boolean => {
        if (transaction.changed.size === 1) {
            const [yObj, changedSet] = transaction.changed.entries().next().value;
            const isChangeSingleProps = yObj instanceof Y.XmlElement && changedSet.size === 1;
            // check if the transaction is only changing the local content.
            // otherwise it might be another changes like create a new SubViewportNode
            if (isChangeSingleProps && changedSet.has('__localContent')) {
                return false;
            }

            // ignore the `__imgUploading` flag of ImageNode
            if (isChangeSingleProps && changedSet.has('__imgUploading')) {
                return false;
            }
        }

        return true;
    };

    /**
     * When word doc is newly created, we will auto init the root with an empty paragraph node.
     * The history item from that init process must be ignored because it will cause the
     * infinite loop of undo <-> init root (lexical will auto handle this init one we start editing).
     * We also can not mark that init update with `historic` tag as it will cause weird behavior
     * when undo the first paragraph. So use this flag as a workaround to manually skip adding history item
     */
    initDocFromEmptyState() {
        this.skipNextInitItem = true;
    }

    override destroy(): void {
        super.destroy();

        this.undoManager.off('stack-item-added', this.onStackItemAdded);
        this.undoManager.off('stack-item-popped', this.onStackItemPopped);
        this.undoManager.off('stack-cleared', this.onStackClear);
    }

    /**
     * Update the undo and redo states of @lexical/history by the state of Yjs UndoManager
     */
    private updateUndoRedoStates = () => {
        this.lexical.dispatchCommand(CAN_UNDO_COMMAND, this.undoManager.undoStack.length > 0);
        this.lexical.dispatchCommand(CAN_REDO_COMMAND, this.undoManager.redoStack.length > 0);
    };

    /**
     * Handle `stack-item-added` event of Yjs UndoManager.
     * We will relied on this to add our own history item corresponding to the added undo operation of Yjs.
     * That way our undo/redo operation can be delegated to Yjs UndoManager in the correct order.
     *
     * We also ignore some unwanted history item when lexical auto init the doc from empty state
     * which can cause infinite loop of undo <-> init root
     */
    private onStackItemAdded = (ev: StackItemEvent) => {
        if (ev.type === 'undo' && !this.undoManager.redoing) {
            // skip the history item when init doc from empty state, lexical will add an unwanted history item -> ignore
            if (this.skipNextInitItem && this.isEmptyState()) {
                this.skipNextInitItem = false;
                this.undoManager.clear(true, false); // clear undo stack
                return;
            }

            // after undo to clear all elements, lexical can auto init an empty paragraph which trigger unwated history item -> ignore.
            const isUndoRecently = this.lastUndoTime && performance.now() - this.lastUndoTime < 100;
            if (isUndoRecently && this.isEmptyState()) {
                console.log('init doc from undo - Skip history');
                return;
            }

            const historyItem = new WordHistoryItem(this.wordEditor);
            historyItem.type = 'word-editing';
            historyItem.viewportId = this.viewportId;
            historyItem.docId = this.docCtrl.state.id;

            this.wordEditor.addHistoryItem(historyItem);
        }

        this.updateUndoRedoStates();
    };

    /**
     * Handle `stack-item-popped` event of Yjs UndoManager.
     * Currently it won't do much because when we undo/redo, our HistoryManager will also pop the history item.
     * Only contains a workaround to prevent an infinite loop of undo <-> init root.
     * @param ev
     */
    private onStackItemPopped = (ev: StackItemEvent) => {
        if (ev.type === 'undo') {
            this.lastUndoTime = performance.now();
        }

        this.updateUndoRedoStates();
    };

    /**
     * When yjs undo/redo stack is cleared, we will also clear our history stack.
     * Otherwise our history item will have no corresponding undo/redo operation in yjs and will cause an unexpected behavior.
     */
    private onStackClear = (ev: { undoStackCleared: boolean; redoStackCleared: boolean }) => {
        const historyManager = this.historyFeature?.getHistoryManager(this.viewportId);
        if (historyManager) {
            historyManager.clearWithCondition((item, fromStack) => {
                if (!(item instanceof WordHistoryItem)) return false;

                const matchStack =
                    (fromStack === 'undo' && ev.undoStackCleared) || (fromStack === 'redo' && ev.redoStackCleared);

                return (
                    item.type === 'word-editing' &&
                    item.viewportId === this.viewportId &&
                    item.docId === this.docCtrl.state.id &&
                    matchStack
                );
            });
        }

        this.updateUndoRedoStates();
    };

    /**
     * Check if current doc is in empty state which contains only 2 nodes: root and empty paragraph.
     * That means current doc is just init from scratch, and the history element from that init process should be ignored.
     * Otherwise it will cause an infinite loop when undo - auto init again.
     */
    private isEmptyState(): boolean {
        return this.wordLib.lexical.read(() => {
            const root = $getRoot();
            const rootChildren = root.getChildren();
            if (rootChildren.length === 0) return true;
            if (rootChildren.length > 1) return false;

            const rootChild = rootChildren[0];
            if ($isParagraphNode(rootChild) && rootChild.getChildrenSize() === 0) return true;

            return false;
        });
    }
}
