import {
    CmdChannel,
    CmdGateway,
    CommonToolbar,
    CoordinatorEvent,
    DefaultDummyCmdGateway,
    DefaultEventEmitter,
    DefaultRegistryManager,
    DefaultVDocCtrl,
    DocumentEditor,
    DocumentEditorFactory,
    DocumentId,
    EditorAddedCES,
    EditorConfig,
    EditorCoordinator,
    EditorCoordinatorConfig,
    EditorId,
    EditorLookup,
    EditorType,
    loadRemoteModule,
    LoadRemoteModuleOptions,
    loadThemeModules,
    RegistryManager,
    ToolBar,
    VEventListener,
    ViErrEventEmitter,
    ViewportDisableCES,
    ViewportEnabledCES,
    ViewportId,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import {
    combineHtmlToBlob,
    createCoordinatorConvertOptions,
    executeOnNextFrame,
    htmlListToPDF,
    htmlToBlob,
} from './utils';

export abstract class BaseCoordinator implements EditorCoordinator {
    /**
     * Map contains all the editor, an editor of a specific type is only added once
     * Use the editor type as id
     */
    private readonly _editors: Map<EditorType, DocumentEditor> = new Map();

    protected readonly _cmdGateway: CmdGateway = new DefaultDummyCmdGateway();
    protected readonly _coordEventEmitter: DefaultEventEmitter<CoordinatorEvent> = new DefaultEventEmitter();

    readonly regMan: RegistryManager;

    constructor(
        public conf: EditorCoordinatorConfig,
        protected readonly errorHandlerEmitter?: ViErrEventEmitter
    ) {
        this.regMan = new DefaultRegistryManager();
    }

    async removeDocByGlobalId(vpId: ViewportId, editorType: EditorType, docGlobalId: DocumentId): Promise<void> {}

    async selectDocByGlobalId(
        vpId: ViewportId,
        editorType: EditorType,
        docGlobalId: DocumentId,
        multi?: boolean
    ): Promise<void> {}

    async switchViewportMode(vpId: ViewportId, mode: ViewportMode) {
        const vm = this.getViewportManager(vpId);

        // ignore if vm is already removed or mode is the same
        if (!vm || mode == vm.mode) return;

        if (mode == 'EditMode') {
            vm.enable('EditMode');

            const eventState: ViewportEnabledCES = { vmId: vm.id };

            await this.coordEventEmitter.emit({
                eventType: 'viewport-edit-mode',
                source: this,
                state: eventState,
            });
        }

        if (mode == 'ViewMode' || mode == 'InteractiveMode') {
            vm.enable(mode);

            const eventState: ViewportEnabledCES = { vmId: vm.id };
            await this.coordEventEmitter.emit({
                eventType: mode == 'ViewMode' ? 'viewport-view-mode' : 'viewport-interactive-mode',
                source: this,
                state: eventState,
            });
        }

        if (mode == 'Disabled') {
            vm.disable();
            const eventState: ViewportDisableCES = {
                flushCmd: false,
                vmId: vm.id,
                cleanSelection: true,
            };
            // select current active viewport to show in UI
            await this.coordEventEmitter.emit({
                eventType: 'viewport-disabled',
                source: this,
                state: eventState,
            });
        }
    }

    get editors(): Map<EditorType, DocumentEditor> {
        return this._editors;
    }

    abstract createToolbar(): ToolBar<any, any>;

    private getEditorById(id: EditorId): DocumentEditor {
        for (const edType of this.editors.keys()) {
            if (id == this.editors.get(edType).id) return this.editors.get(edType);
        }
        return null;
    }

    editor(id: EditorId): DocumentEditor {
        return this.getEditorById(id);
    }

    editorByType(type: EditorType): DocumentEditor {
        return this.editors.get(type);
    }

    get cmdGateway(): CmdGateway {
        return this._cmdGateway;
    }

    get coordEventEmitter(): DefaultEventEmitter<CoordinatorEvent> {
        return this._coordEventEmitter;
    }

    async loadEditor(lookup: EditorLookup): Promise<DocumentEditor> {
        if (this.editors.has(lookup.editorType))
            throw new Error(`Editor of type ${lookup.editorType} has already been added before`);

        const options: LoadRemoteModuleOptions = {
            remoteEntry: lookup.lookup.remoteEntry,
            remoteName: lookup.lookup.remoteName,
            exposedModule: lookup.lookup.exposedModule || 'editorImpl',
        };

        // TODO: support initialization of module err handler with an emitter to listen to the error events from the module
        // here, would return loadRemoteModule the object defined by export default in the MFE of the module
        // i.e. for geo editor this object is exported { factoryFn : geoEditorFactory, errHandlerInitializer : .... }
        const rmModule = await loadRemoteModule<DocumentEditorFactory>(options);
        const editor = rmModule.factoryFn(this.generateEditorConfig(lookup), this);
        if (this.errorHandlerEmitter && rmModule.errHandlerInitializer)
            rmModule.errHandlerInitializer(this.errorHandlerEmitter);

        if (lookup.editorStyles?.length) {
            await loadThemeModules(lookup.editorStyles);
        }

        return editor;
    }

    async addEditorInstance(editor: DocumentEditor, editorType: EditorType): Promise<EditorType> {
        await editor.initialize();

        this.editors.set(editorType, editor);
        this.coordEventEmitter.emit({
            eventType: 'editor-added',
            source: this,
            state: { editor: editor } as EditorAddedCES,
        });

        return editorType;
    }

    async addEditor(lookup: EditorLookup): Promise<EditorType> {
        const editor = await this.loadEditor(lookup);
        return await this.addEditorInstance(editor, lookup.editorType);
    }

    protected generateEditorConfig(lookup: EditorLookup): EditorConfig {
        const editorData = this.conf.editorTypeMapping[lookup.editorType];

        if (!editorData) throw new Error(`Editor mapping for ${lookup.editorType} is not available!`);

        const config: EditorConfig = {
            id: editorData.id,
            editorType: lookup.editorType,
            channelCode: editorData.channelCode ? editorData.channelCode : editorData.id, // use the configured channel code, if not available, use the id as the channel code
            apiUri: '',
            commandGateway: this.cmdGateway,
            registryManager: this.regMan,
        };

        // if additional setting is available
        // merge it with config
        if (lookup.settings) {
            for (const key in lookup.settings) {
                config[key] = lookup.settings[key];
            }
        }

        // generate default values if not set
        if (!config.docViewMode) config.docViewMode = 'bounded';

        return config;
    }

    registerCmdChannel(channelCode: number): CmdChannel {
        return this.cmdGateway.registerChannel(channelCode);
    }

    abstract getViewportManager(viewportId: string): ViewportManager;

    abstract removeViewport(vpId: ViewportId): Promise<void>;

    abstract initialize(): Promise<void>;

    abstract start(): Promise<void>;

    abstract stop(): Promise<void>;

    abstract getCommonToolbar(viewportId: string): CommonToolbar;

    abstract getEditorToolbar(viewportId: string, editorType: EditorType): ToolBar<any, any>;

    registerCoordEventListener(listener: VEventListener<CoordinatorEvent>) {
        this.coordEventEmitter.registerListener(listener);
    }

    unregisterCoordEventListener(listener: VEventListener<CoordinatorEvent>) {
        this.coordEventEmitter.unregisterListener(listener);
    }

    async captureViewportPreview(viewportId: ViewportId): Promise<Blob> {
        const vm = this.getViewportManager(viewportId);
        if (!vm) return null;

        const originalVmMode = vm.mode;
        try {
            this.switchViewportMode(viewportId, 'InteractiveMode');

            return await executeOnNextFrame(() =>
                htmlToBlob(vm.rootEl, {
                    ...createCoordinatorConvertOptions(this),
                    filter: node => !node?.classList?.contains('v-toolbar'), // ignore toolbar
                    imageProxyPrefix: this.conf.imageProxyPrefix,
                    style: { transform: 'none' },
                })
            );
        } catch (e) {
            console.error('Failed to capture preview', e);
            return null;
        } finally {
            this.switchViewportMode(viewportId, originalVmMode);
        }
    }

    async printPDF(viewportId: ViewportId): Promise<void> {
        const vm = this.getViewportManager(viewportId);
        if (!vm) return;

        const originalVmMode = vm.mode;
        try {
            this.switchViewportMode(viewportId, 'ViewMode');

            await executeOnNextFrame(() =>
                htmlListToPDF([vm.rootEl as HTMLElement], createCoordinatorConvertOptions(this, 'PDF'))
            );
        } catch (e) {
            console.error('Failed to print PDF', e);
        } finally {
            this.switchViewportMode(viewportId, originalVmMode);
        }
    }

    async printDocPDF(editorType: EditorType, viewportId: ViewportId, docId: DocumentId): Promise<void> {
        const editor = this.editorByType(editorType);
        if (!editor) return;

        const doc = editor.findDocumentByGlobalId(viewportId, docId) as DefaultVDocCtrl;
        if (!doc) return;

        const docElements = doc.layers.map(layer => layer.nativeEl).filter(Boolean);

        if (!docElements.length) return;

        const vm = this.getViewportManager(viewportId);
        const originalVmMode = vm.mode;

        try {
            this.switchViewportMode(viewportId, 'ViewMode');

            await executeOnNextFrame(() =>
                htmlListToPDF(docElements as HTMLElement[], createCoordinatorConvertOptions(this, 'PDF'))
            );
        } catch (e) {
            console.error('Failed to print PDF', e);
        } finally {
            this.switchViewportMode(viewportId, originalVmMode);
        }
    }

    async captureDocumentPreview(editorType: EditorType, viewportId: ViewportId, docId: DocumentId): Promise<Blob> {
        const editor = this.editorByType(editorType);
        if (!editor) return null;

        const doc = editor.findDocumentByGlobalId(viewportId, docId) as DefaultVDocCtrl;
        if (!doc) return null;

        const docElements = doc.layers.map(layer => layer.nativeEl).filter(Boolean);

        if (!docElements.length) return null;

        const vm = this.getViewportManager(viewportId);
        const originalVmMode = vm.mode;

        try {
            this.switchViewportMode(viewportId, 'InteractiveMode');

            if (docElements.length > 1)
                return await combineHtmlToBlob(docElements, {
                    ...createCoordinatorConvertOptions(this),
                    backgroundColor: '#fff',
                    imageProxyPrefix: this.conf.imageProxyPrefix,
                    style: { transform: 'none' },
                });

            // get doc element, if the element is svg with foreign object (Ex: inside classroom) -> use the foreignObject child
            let element = docElements[0];
            if (
                element instanceof SVGElement &&
                element.children.length === 1 &&
                element.children[0]?.matches('foreignObject') &&
                element.children[0].children.length === 1 &&
                element.children[0].children[0] instanceof HTMLElement
            ) {
                element = element.children[0].children[0];
            }

            const result = await executeOnNextFrame(() =>
                htmlToBlob(element, {
                    ...createCoordinatorConvertOptions(this),
                    backgroundColor: '#fff',
                    imageProxyPrefix: this.conf.imageProxyPrefix,
                    style: { transform: 'none' },
                })
            );
            this.switchViewportMode(viewportId, originalVmMode);
            return result;
        } catch (e) {
            this.switchViewportMode(viewportId, originalVmMode);
            throw e;
        }
    }
}
