import { CmdMeta, EditorType } from '@viclass/editor.core';
import axios from 'axios';

/**
 * A data saver that directly save data to backend of editors
 */
export class DirectBackendDataSaver {
    editorMapping: Map<number, EditorType> = new Map();

    constructor(
        private bkEnd: Map<EditorType, string>,
        edMapping: {
            [editorType: string]: { id: number; channelCode?: number };
        }
    ) {
        for (const key in edMapping) {
            this.editorMapping.set(edMapping[key].id, key as EditorType);
        }
    }

    /**
     * TODO : put command into a queue and execute in sequence to ensure the integrity of the internal document
     *
     * This method save the serialized commands from internal editors to the editor backend
     */
    saveData(cmdMeta: CmdMeta, binary: Uint8Array, edChannelCode: number, globalId: string) {
        const edType = this.editorMapping.get(edChannelCode);
        const editorAPI = this.bkEnd.get(edType);

        if (!editorAPI)
            throw new Error(`Unable to find the backend API of internal editor ${edType} while trying to save data.`);

        if (this.validateToSave(cmdMeta)) {
            // conventionally, the command is accepted at
            axios.post(`${editorAPI}/cmd`, binary, {
                headers: {
                    'Content-Type': 'application/octet-stream',
                },
                params: {
                    globalId: globalId,
                },
                responseType: 'json',
            });
        }
    }

    addBkEnd(ed: EditorType, api: string) {
        this.bkEnd.set(ed, api);
    }

    validateToSave(meta: CmdMeta): boolean {
        if (!meta.sequence || !meta.versionable) {
            return false;
        }
        return true;
    }
}
