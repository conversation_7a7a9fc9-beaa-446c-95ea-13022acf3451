import {
    BoardViewportManager,
    BoundaryRectangle,
    BoundedGraphicLayerCtrl,
    DefaultVDocCtrl,
    HasBoundaryCtrl,
    LocatableEvent,
    mouseLocation,
} from '@viclass/editor.core';

import { BehaviorSubject, Observable } from 'rxjs';
import { GeometryEditor } from '../geo.editor';
import { DefaultGeoRenderProp, DocRenderProp, GeoRenderDocState, GeoRenderElement } from '../model';
import { GeoDoc, GeoLayer } from '../model/geo.models';
import { GeoRenderer, RENDER_VERTEX_RADIUS, RENDER_VERTEX_RADIUS_POTENTIAL } from '../renderer';
import { calculatePosInLayer, validatePointerPos } from '../tools/util.tool';
import {
    checkHitOnElementsBoundary2D,
    checkHitOnElementsInside2D,
    checkHitOnPoints,
    GeoSelectHitContext,
} from './index';

export class GeoDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    rendererCtrl: GeoRenderer;

    private readonly _docRenderProp$: BehaviorSubject<DocRenderProp>;

    private readonly _selectedElements$: BehaviorSubject<GeoRenderElement[]>;
    public readonly selectedElements$: Observable<GeoRenderElement[]>;
    get selectedElements() {
        return this._selectedElements$.getValue();
    }

    private readonly _docDefaultElRenderProps$: BehaviorSubject<DefaultGeoRenderProp>;

    constructor(
        public override editor: GeometryEditor,
        public override state: GeoDoc,
        public override viewport: BoardViewportManager
    ) {
        super(state, editor, viewport);

        this._docRenderProp$ = new BehaviorSubject<DocRenderProp>(state.docRenderProp);

        this._docDefaultElRenderProps$ = new BehaviorSubject<DefaultGeoRenderProp>(state.docDefaultElRenderProps);

        this._selectedElements$ = new BehaviorSubject<GeoRenderElement[]>([]);
        this.selectedElements$ = this._selectedElements$.asObservable();
    }

    get docRenderProp$(): Observable<DocRenderProp> {
        return this._docRenderProp$;
    }

    get docDefaultElRenderProps$(): Observable<DefaultGeoRenderProp> {
        return this._docDefaultElRenderProps$;
    }

    updateSelectedElements(sel: GeoRenderElement[]) {
        this._selectedElements$.next(sel);
    }

    updateDocRenderProp(docRenderProp: DocRenderProp) {
        this.state.docRenderProp = docRenderProp;
        this._docRenderProp$.next(this.state.docRenderProp);
    }

    updateDocDefaultElRenderProps(docDefaultElRenderProps: DefaultGeoRenderProp) {
        this.state.docDefaultElRenderProps = docDefaultElRenderProps;
        this._docDefaultElRenderProps$.next(this.state.docDefaultElRenderProps);
    }

    clearUnusableObject() {
        this.rendererCtrl?.clearUnusableObject();
    }

    attachRenderer(r: GeoRenderer) {
        this.rendererCtrl = r;
        this.layers.push(r.layer);
        this.state.layers.push(r.layer.state as GeoLayer);
    }

    onRemove() {
        const idx = this.layers.indexOf(this.rendererCtrl.layer);
        this.state.layers.splice(idx, 1);
        this.layers.splice(idx, 1);
        this.rendererCtrl.onRemoved();
        this.viewport.removeLayer(this.rendererCtrl.layer);
    }

    // TODO: this it checking implementation is a slow implementation
    // upgrade to using quad tree or something
    checkHit(
        event: LocatableEvent<any>,
        l: BoundedGraphicLayerCtrl,
        useRelaxedHitPrecision = false,
        preview = false
    ): GeoSelectHitContext {
        const isTouchEvent = event.nativeEvent['pointerType'] == 'touch';

        const v = l.viewport as BoardViewportManager;
        const zoomLevel = v.zoomLevel;
        const renderVertexRadius = useRelaxedHitPrecision ? RENDER_VERTEX_RADIUS_POTENTIAL : RENDER_VERTEX_RADIUS;
        // 2 pixel precision, meaning if the mouse is
        // 2 pixel away from the center. This is the base precision without taking
        // into account zoom level, point size, line width
        const basePixelPrecision =
            isTouchEvent && !useRelaxedHitPrecision ? renderVertexRadius * 1.6 : renderVertexRadius;
        // if it is touch, we need bigger precision for point
        const basePointPixelPrecision =
            isTouchEvent && !useRelaxedHitPrecision ? basePixelPrecision * 3 : basePixelPrecision;

        const renderer = this.rendererCtrl;
        if (renderer.layer == l) {
            const mousePos = mouseLocation(event);
            if (!validatePointerPos(mousePos, this)) return undefined;

            const docRenderProp = this.state.docRenderProp;
            const mousePosInLayer = calculatePosInLayer(mousePos, this);
            const mousePosInGeo = renderer.layerToGeoPos(mousePosInLayer);
            // the precision when checking hit
            const epsilon = (basePixelPrecision / (docRenderProp.screenUnit * docRenderProp.scale)) * zoomLevel;
            const epsilonPoint =
                (basePointPixelPrecision / (docRenderProp.screenUnit * docRenderProp.scale)) * zoomLevel;

            return (
                checkHitOnElementsInside2D(this, renderer.angleElements(false), mousePosInGeo, renderer)[0] ??
                checkHitOnPoints(
                    this,
                    renderer.pointElements(preview),
                    mousePosInGeo,
                    renderer,
                    v.zoomLevel,
                    epsilonPoint
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.lineElements(preview),
                    mousePosInGeo,
                    renderer,
                    v.zoomLevel,
                    epsilon
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.sectorElements(preview),
                    mousePosInGeo,
                    renderer,
                    v.zoomLevel,
                    epsilon
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.circleElements(preview),
                    mousePosInGeo,
                    renderer,
                    v.zoomLevel,
                    epsilon
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.ellipseElements(preview),
                    mousePosInGeo,
                    renderer,
                    v.zoomLevel,
                    epsilon
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.shapeElements(preview),
                    mousePosInGeo,
                    renderer,
                    v.zoomLevel,
                    epsilon
                )[0] ??
                checkHitOnElementsInside2D(this, renderer.shapeElements(preview), mousePosInGeo, renderer)[0] ?? {
                    doc: this,
                    hitDetails: undefined,
                }
            );
        }

        return undefined;
    }

    unselect() {
        if (this.layers[0]) this.viewport.sink(this.layers[0]);
    }

    select() {
        if (this.layers[0]) this.viewport.float(this.layers[0]);
    }

    destroy() {}

    updateBoundary(boundary: BoundaryRectangle) {
        for (let i = 0; i < this.state.layers.length; i++) {
            // update layer state
            const layer = this.layers[i];
            this.state.layers[i].boundary = boundary; // update all layers state

            if (this.isBoundedView())
                // in full mode, the layer is an unbounded graphic layer
                (layer as BoundedGraphicLayerCtrl).updateBoundary(boundary);
        }
    }

    buildGeoRenderDocState(): GeoRenderDocState {
        return {
            docId: this.state.globalId,
            canvasWidth: this.rendererCtrl.width,
            canvasHeight: this.rendererCtrl.height,
            numDim: this.state.kind,
            ...this.state.docRenderProp,
        } as GeoRenderDocState;
    }

    isBoundedView() {
        return this.editor.geoEditorConf.docViewMode == 'bounded';
    }

    getBoundary(): BoundaryRectangle {
        return this.state.layers?.[0]?.boundary;
    }
}
