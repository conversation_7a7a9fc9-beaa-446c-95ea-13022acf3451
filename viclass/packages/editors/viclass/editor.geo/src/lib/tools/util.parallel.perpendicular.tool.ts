import { point, vector } from '@flatten-js/core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeoEpsilon, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vert, vertex, vertexOnStroke, VertexOnStroke } from '../selectors';
import { strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { createFlattenLine } from './util.flatten';
import {
    calculateLineCircleIntersection,
    calculateLineLineIntersection,
    intersectionLineEllipse,
} from './util.intersections';
import { sortOnLineV2 } from '../nth.direction';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementCurve,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Base class for line-based geometry tools (parallel, perpendicular, etc.)
 * Contains shared functionality for tools that create lines based on existing lines and points
 */
export abstract class BaseParallelPerpendicularTool extends GeometryTool<CommonToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    directionVector: number[] | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.directionVector = undefined;
        super.resetState();
    }

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void;
    protected abstract buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): any;
    protected abstract buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): any;
    protected abstract buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): any;
    protected abstract getSimpleConstructionLabel(): string;
    protected abstract getComplexConstructionLabel(): string;
    protected abstract buildCurvedElementConstruction(
        combinedName: string,
        baseLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        nthIntersection?: number
    ): any;

    /**
     * Creates the selection logic using selector pattern with preview
     * Following Pattern: Line -> Point -> Preview -> Final Point Selection
     */
    protected createSelLogic() {
        // First selector: select a line
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderVector', 'RenderLine', 'RenderLineSegment', 'RenderRay'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: select a point to define through which the line passes
        const firstPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Third selector: enhanced vertex selector with projection for final point on line
        const finalVertexSelector = or(
            [
                // Option 1: Select free vertex with projection onto line
                vertex({
                    preview: true, // Allow selecting preview elements (including first point if it was a preview)
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnLine(previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, doc) =>
                        this.projectVertexOnStrokeToIntersection(stroke, previewVertex, doc),
                    cfunc: (stroke, doc) => this.checkStrokeIntersection(stroke, doc),
                    refinedFilter: (_el: any) =>
                        [
                            'RenderVector',
                            'RenderLine',
                            'RenderLineSegment',
                            'RenderRay',
                            'RenderCircle',
                            'RenderEllipse',
                            'RenderSector',
                        ].includes(_el.type),
                }),
            ],
            { flatten: true }
        );

        // Main selection logic: line -> point -> final vertex
        this.selLogic = then([lineSelector, firstPointSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [line, throughPoint, finalVertexSelection] = selector.selected;

                this.selectedLine = strk(line as RenderLine) as RenderLine;
                this.selectedPoint = vert(throughPoint as RenderVertex);
                const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                // Check if final vertex is the same as the through point
                if (this.selectedPoint.relIndex === finalVertex.relIndex) {
                    // If vertices are the same, use simple line construction
                    if (!this.previewLine) {
                        console.error('Preview line not available for same point construction');
                        this.resetState();
                        return;
                    }
                    await this.handleSimpleLineConstruction(doc, this.selectedLine, this.selectedPoint);
                } else {
                    await this.handleComplexLineConstruction(
                        doc,
                        this.selectedLine,
                        this.selectedPoint,
                        finalVertex,
                        finalVertexSelection
                    );
                }
            },
        });
    }

    /**
     * Check function to validate that stroke has intersection with preview line
     * Enhanced to support curved elements
     */
    protected checkStrokeIntersection(stroke: StrokeType, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false; // No preview line available

        try {
            // Check for line elements
            if (isElementLine(stroke)) {
                return this.checkLineIntersection(stroke as RenderLine, doc);
            }

            // Check for curved elements
            if (isElementCurve(stroke)) {
                return this.checkCurvedElementIntersection(stroke as RenderCircle | RenderEllipse | RenderSector, doc);
            }

            // Fallback: check if stroke contains the selected point
            if (this.selectedPoint && isElementLine(stroke)) {
                const strokeLine = stroke as RenderLine;
                const fl = createFlattenLine(strokeLine, doc);
                const selectedPt = point(this.selectedPoint.coords[0], this.selectedPoint.coords[1]);
                return fl.intersect(selectedPt).length > 0;
            }

            return false;
        } catch (error) {
            console.warn('Error checking stroke intersection:', error);
            return false;
        }
    }

    /**
     * Transform function to project any point onto the line preview
     */
    protected projectOnLine(previewEl: RenderVertex, _doc: GeoDocCtrl): RenderVertex {
        if (!this.selectedLine || !this.selectedPoint || !this.previewLine || !this.directionVector) {
            return previewEl; // Return original if no line is available
        }

        try {
            const throughPointCoords = this.selectedPoint.coords;

            // Check if the preview point is very close to the through point (same point selection)
            const distance = Math.hypot(
                previewEl.coords[0] - throughPointCoords[0],
                previewEl.coords[1] - throughPointCoords[1]
            );

            // If user clicks on the same point as through point, keep it there
            if (distance < GeoEpsilon) {
                previewEl.coords[0] = throughPointCoords[0];
                previewEl.coords[1] = throughPointCoords[1];
                if (previewEl.coords.length > 2) previewEl.coords[2] = 0;
                return previewEl;
            }

            const projectedCoords = projectPointOntoLine(previewEl.coords, throughPointCoords, this.directionVector);

            if (projectedCoords) {
                previewEl.coords[0] = projectedCoords[0];
                previewEl.coords[1] = projectedCoords[1];
                if (previewEl.coords.length > 2) previewEl.coords[2] = 0; // Z coordinate
            }

            return previewEl;
        } catch (error) {
            console.warn('Error projecting point onto line:', error);
            return previewEl;
        }
    }

    /**
     * Transform function to project vertex on stroke to intersection with line
     * Enhanced to support curved elements
     */
    protected projectVertexOnStrokeToIntersection(
        stroke: StrokeType,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl
    ): RenderVertex {
        if (!this.previewLine) return previewVertex;

        // Don't project if it's the same as preview line
        if (isElementLine(stroke) && this.previewLine.relIndex === (stroke as RenderLine).relIndex) {
            return previewVertex;
        }

        try {
            // Use the enhanced projection method
            return this.projectFinalVertex(previewVertex, stroke, doc);
        } catch (error) {
            console.warn('Error projecting vertex on stroke to intersection:', error);
            return undefined;
        }
    }

    /**
     * Enhanced validation for final vertex selection
     * Validates that the final vertex is appropriate for the construction type
     */
    protected validateFinalVertex(finalVertex: RenderVertex, stroke: StrokeType, doc: GeoDocCtrl): boolean {
        if (!this.previewLine || !this.selectedPoint) return false;

        try {
            // If vertex is on preview line, it's always valid
            if (this.isVertexOnPreviewLine(finalVertex, doc)) {
                return true;
            }

            // If vertex is not on any stroke, project it onto preview line
            if (!stroke) {
                return this.canProjectVertexOntoPreviewLine(finalVertex);
            }

            // If vertex is on a stroke, check if stroke intersects with preview line
            if (isElementLine(stroke)) {
                return this.checkLineIntersection(stroke as RenderLine, doc);
            } else if (isElementCurve(stroke)) {
                return this.checkCurvedElementIntersection(stroke as RenderCircle | RenderEllipse | RenderSector, doc);
            }

            return false;
        } catch (error) {
            console.warn('Error validating final vertex:', error);
            return false;
        }
    }

    /**
     * Check if vertex lies on the preview line
     */
    protected isVertexOnPreviewLine(vertex: RenderVertex, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false;

        try {
            const flattenLine = createFlattenLine(this.previewLine, doc);
            const vertexPoint = point(vertex.coords[0], vertex.coords[1]);
            const intersections = flattenLine.intersect(vertexPoint);
            return intersections.length > 0;
        } catch (error) {
            console.warn('Error checking if vertex is on preview line:', error);
            return false;
        }
    }

    /**
     * Check if vertex can be projected onto preview line
     */
    protected canProjectVertexOntoPreviewLine(vertex: RenderVertex): boolean {
        if (!this.selectedPoint || !this.directionVector) return false;

        // Always allow projection for free vertices
        return vertex.relIndex !== this.selectedPoint.relIndex;
    }

    /**
     * Check if line intersects with preview line
     */
    protected checkLineIntersection(line: RenderLine, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false;

        try {
            const intersections = calculateLineLineIntersection(this.previewLine, line, doc);
            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking line intersection:', error);
            return false;
        }
    }

    /**
     * Check if curved element intersects with preview line
     */
    protected checkCurvedElementIntersection(
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        doc: GeoDocCtrl
    ): boolean {
        if (!this.previewLine) return false;

        try {
            switch (curvedElement.type) {
                case 'RenderCircle':
                    const circleIntersections = calculateLineCircleIntersection(this.previewLine, curvedElement, doc);
                    return circleIntersections && circleIntersections.length > 0;

                case 'RenderEllipse':
                    const ellipseIntersections = intersectionLineEllipse(this.previewLine, curvedElement, doc);
                    return ellipseIntersections && ellipseIntersections.length > 0;

                case 'RenderSector':
                    // For sectors, we need to check intersection with the arc
                    // This is more complex and may require custom intersection logic
                    return this.checkSectorIntersection(curvedElement, doc);

                default:
                    return false;
            }
        } catch (error) {
            console.warn('Error checking curved element intersection:', error);
            return false;
        }
    }

    /**
     * Check if sector intersects with preview line
     * This is a simplified implementation - may need enhancement for precise arc intersection
     */
    protected checkSectorIntersection(sector: RenderSector, doc: GeoDocCtrl): boolean {
        // For now, treat sector as a circle and check intersection
        // In a full implementation, you'd need to check if intersections lie within the sector arc
        try {
            // Use the sector directly as it has the same intersection properties as a circle for line intersection
            const intersections = calculateLineCircleIntersection(this.previewLine, sector as any, doc);
            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking sector intersection:', error);
            return false;
        }
    }

    /**
     * Project final vertex onto preview line or find intersection with curved elements
     */
    protected projectFinalVertex(finalVertex: RenderVertex, stroke: StrokeType, doc: GeoDocCtrl): RenderVertex {
        if (!this.previewLine || !this.selectedPoint) return finalVertex;

        try {
            // If no stroke, project onto preview line
            if (!stroke) {
                return this.projectVertexOntoPreviewLine(finalVertex);
            }

            // If stroke is a line, find intersection
            if (isElementLine(stroke)) {
                return this.projectFinalVertexOnLine(finalVertex, stroke as RenderLine, doc);
            }

            // If stroke is a curved element, find intersection
            if (isElementCurve(stroke)) {
                return this.projectFinalVertexOnCurvedElement(
                    finalVertex,
                    stroke as RenderCircle | RenderEllipse | RenderSector,
                    doc
                );
            }

            return finalVertex;
        } catch (error) {
            console.warn('Error projecting final vertex:', error);
            return finalVertex;
        }
    }

    /**
     * Project vertex onto preview line
     */
    protected projectVertexOntoPreviewLine(vertex: RenderVertex): RenderVertex {
        if (!this.selectedPoint || !this.directionVector) return vertex;

        try {
            const projectedCoords = projectPointOntoLine(
                vertex.coords,
                this.selectedPoint.coords,
                this.directionVector
            );
            if (projectedCoords) {
                vertex.coords[0] = projectedCoords[0];
                vertex.coords[1] = projectedCoords[1];
                if (vertex.coords.length > 2) vertex.coords[2] = 0;
            }
            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex onto preview line:', error);
            return vertex;
        }
    }

    /**
     * Project final vertex on line stroke to intersection point
     */
    protected projectFinalVertexOnLine(vertex: RenderVertex, line: RenderLine, doc: GeoDocCtrl): RenderVertex {
        if (!this.previewLine) return vertex;

        try {
            const intersections = calculateLineLineIntersection(this.previewLine, line, doc);
            if (intersections && intersections.length > 0) {
                // For line-line intersection, there's typically only one intersection
                // Use first intersection (no ordering needed for single point)
                const intersection = intersections[0];
                vertex.coords[0] = intersection.x;
                vertex.coords[1] = intersection.y;
                if (vertex.coords.length > 2) vertex.coords[2] = 0;
            }
            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex on line:', error);
            return vertex;
        }
    }

    /**
     * Project final vertex on curved element to intersection point
     */
    protected projectFinalVertexOnCurvedElement(
        vertex: RenderVertex,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        doc: GeoDocCtrl
    ): RenderVertex {
        if (!this.previewLine) return vertex;

        try {
            let intersections: any[] = [];

            switch (curvedElement.type) {
                case 'RenderCircle':
                    intersections = calculateLineCircleIntersection(this.previewLine, curvedElement, doc) || [];
                    break;

                case 'RenderEllipse':
                    intersections = intersectionLineEllipse(this.previewLine, curvedElement, doc) || [];
                    break;

                case 'RenderSector':
                    // For sectors, get circle intersections and filter by arc
                    const circleIntersections =
                        calculateLineCircleIntersection(this.previewLine, curvedElement as any, doc) || [];
                    intersections = this.filterSectorIntersections(circleIntersections, curvedElement, doc);
                    break;
            }

            if (intersections.length > 0) {
                // For projection, use closest intersection to current vertex position
                const selectedIntersection = this.selectIntersectionForProjection(vertex, intersections);

                if (selectedIntersection) {
                    vertex.coords[0] = selectedIntersection.x;
                    vertex.coords[1] = selectedIntersection.y;
                    if (vertex.coords.length > 2) vertex.coords[2] = 0;
                }
            }

            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex on curved element:', error);
            return vertex;
        }
    }

    /**
     * Orders intersections using the same logic as intersection.point.tool.ts and backend
     * This ensures consistent ordering with backend and intersection tool
     */
    protected getOrderedIntersections(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        intersections: any[],
        doc: GeoDocCtrl
    ): any[] {
        if (!intersections || intersections.length <= 1) return intersections;

        // Convert intersection points to Point objects for ordering
        const intersectionPoints = intersections.map(pt => point(pt.x, pt.y));

        // Apply the same ordering logic as intersection.point.tool.ts and backend
        // For Line-Circle, Line-Sector, Line-Ellipse: order by parallel vector
        const orderedVector = previewLine.orderedVector(doc.rendererCtrl);
        const directionVector = vector(orderedVector[0], orderedVector[1]);
        const orderedPoints = sortOnLineV2(directionVector, intersectionPoints);

        // Convert back to intersection format
        return orderedPoints.map(pt => ({ x: pt.x, y: pt.y }));
    }

    /**
     * Find the closest intersection point to the given vertex
     * This is used for projection operations where we want the most natural/closest intersection
     */
    protected findClosestIntersection(vertex: RenderVertex, intersections: any[]): any {
        if (intersections.length === 1) return intersections[0];

        let closestIntersection = intersections[0];
        let minDistance = Math.hypot(intersections[0].x - vertex.coords[0], intersections[0].y - vertex.coords[1]);

        for (let i = 1; i < intersections.length; i++) {
            const distance = Math.hypot(intersections[i].x - vertex.coords[0], intersections[i].y - vertex.coords[1]);
            if (distance < minDistance) {
                minDistance = distance;
                closestIntersection = intersections[i];
            }
        }

        return closestIntersection;
    }

    /**
     * Select intersection for construction operations
     * Uses ordered intersections for predictable construction behavior consistent with backend
     */
    protected selectIntersectionForConstruction(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        intersections: any[],
        nthIntersection: number | undefined,
        doc: GeoDocCtrl
    ): any {
        if (!intersections || intersections.length === 0) return null;

        // Get ordered intersections using the same logic as backend and intersection.point.tool.ts
        const orderedIntersections = this.getOrderedIntersections(previewLine, curvedElement, intersections, doc);

        // Use nth intersection if specified and valid
        const actualNthIntersection = nthIntersection !== undefined ? nthIntersection : 0;

        // Validate nth parameter is within bounds (same as backend)
        if (actualNthIntersection < 0 || actualNthIntersection >= orderedIntersections.length) {
            console.warn(
                `Chỉ số giao điểm ${actualNthIntersection + 1} không hợp lệ. Chỉ có ${
                    orderedIntersections.length
                } giao điểm.`
            );
            return orderedIntersections[0]; // Fallback to first intersection
        }

        return orderedIntersections[actualNthIntersection];
    }

    /**
     * Select intersection for projection operations (real-time cursor movement)
     * Uses closest intersection for natural user experience - vertex snaps to nearest intersection
     * This is different from construction which uses ordered intersections for consistency
     */
    protected selectIntersectionForProjection(vertex: RenderVertex, intersections: any[]): any {
        if (!intersections || intersections.length === 0) return null;
        // For projection, always use closest intersection to provide natural UX
        return this.findClosestIntersection(vertex, intersections);
    }

    /**
     * Calculate the nth intersection index based on the final vertex position
     * This determines which intersection the user actually selected through projection
     */
    protected calculateNthIntersectionFromVertex(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        finalVertex: RenderVertex,
        doc: GeoDocCtrl
    ): number {
        try {
            // Calculate all intersections between preview line and curved element
            let allIntersections: any[] = [];

            switch (curvedElement.type) {
                case 'RenderCircle':
                    allIntersections = calculateLineCircleIntersection(previewLine, curvedElement, doc) || [];
                    break;

                case 'RenderEllipse':
                    allIntersections = intersectionLineEllipse(previewLine, curvedElement, doc) || [];
                    break;

                case 'RenderSector':
                    const circleIntersections =
                        calculateLineCircleIntersection(previewLine, curvedElement as any, doc) || [];
                    allIntersections = this.filterSectorIntersections(circleIntersections, curvedElement, doc);
                    break;
            }

            if (allIntersections.length <= 1) return 0;

            // Get ordered intersections using the same logic as backend
            const orderedIntersections = this.getOrderedIntersections(
                previewLine,
                curvedElement,
                allIntersections,
                doc
            );

            // Find which ordered intersection is closest to the final vertex position
            let closestIndex = 0;
            let minDistance = Math.hypot(
                orderedIntersections[0].x - finalVertex.coords[0],
                orderedIntersections[0].y - finalVertex.coords[1]
            );

            for (let i = 1; i < orderedIntersections.length; i++) {
                const distance = Math.hypot(
                    orderedIntersections[i].x - finalVertex.coords[0],
                    orderedIntersections[i].y - finalVertex.coords[1]
                );
                if (distance < minDistance) {
                    minDistance = distance;
                    closestIndex = i;
                }
            }

            return closestIndex;
        } catch (error) {
            console.warn('Error calculating nth intersection from vertex:', error);
            return 0; // Fallback to first intersection
        }
    }

    /**
     * Filter circle intersections to only include those within the sector arc
     * This is a simplified implementation - may need enhancement for precise arc filtering
     */
    protected filterSectorIntersections(intersections: any[], sector: RenderSector, doc: GeoDocCtrl): any[] {
        // For now, return all intersections
        // In a full implementation, you'd check if each intersection point lies within the sector's arc
        return intersections;
    }

    /**
     * Handle curved element construction with enhanced logic
     * Uses consistent intersection ordering and nth intersection selection
     */
    protected handleCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        line: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        nthIntersection?: number
    ): any {
        // This method can be overridden by subclasses for specific curved element handling
        // Default implementation uses the abstract buildCurvedElementConstruction method
        const combinedName = `${throughPoint.name}${finalVertex.name}`;

        // For construction, use ordered intersection selection
        // If nth intersection is not specified, use first ordered intersection (not closest)
        const actualNthIntersection = nthIntersection !== undefined ? nthIntersection : 0;

        return this.buildCurvedElementConstruction(
            combinedName,
            line,
            curvedElement,
            throughPoint,
            actualNthIntersection
        );
    }

    /**
     * Handle simple line construction when final vertex is same as through point
     */
    protected async handleSimpleLineConstruction(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex) {
        try {
            // Validate essential prerequisites
            if (!this.previewLine) {
                console.error('previewLine is not available for construction');
                this.resetState();
                return;
            }

            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                this.previewLine
            );

            // Use build line construction
            const construction = this.buildSimpleLineConstruction(this.previewLine.name, line, throughPoint);

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex line construction when final vertex is different from through point
     */
    protected async handleComplexLineConstruction(
        ctrl: GeoDocCtrl,
        line: RenderLine,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        finalVertexSelection: any
    ) {
        try {
            // Validate final vertex before proceeding
            const stroke = strk(finalVertexSelection);
            if (!this.validateFinalVertex(finalVertex, stroke, ctrl)) {
                console.warn('Final vertex validation failed');
                this.resetState();
                return;
            }

            // Project final vertex to appropriate position
            finalVertex = this.projectFinalVertex(finalVertex, stroke, ctrl);

            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel()
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;
            if (isElementLine(stroke)) {
                // Use intersection construction
                const construction = this.buildLineSegmentWithIntersectionConstruction(
                    combinedName,
                    line,
                    stroke as RenderLine,
                    throughPoint
                );
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else if (isElementCurve(stroke)) {
                // Calculate which intersection the user actually selected based on finalVertex position
                const nthIntersection = this.calculateNthIntersectionFromVertex(
                    line,
                    stroke as RenderCircle | RenderEllipse | RenderSector,
                    finalVertex,
                    ctrl
                );

                console.log('DEBUG: isElementCurve branch - calculated nthIntersection:', nthIntersection);

                // Use curved element construction with the correct nth intersection
                const construction = this.handleCurvedElementConstruction(
                    ctrl,
                    line,
                    stroke as RenderCircle | RenderEllipse | RenderSector,
                    throughPoint,
                    finalVertex,
                    nthIntersection // Use the calculated intersection index
                );

                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else {
                // Use segment construction with scaling factor
                const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

                let k = 0;
                if (startPt && endPt && this.directionVector) {
                    // Calculate vector from through point to final vertex
                    const toEndVector = [
                        finalVertex.coords[0] - throughPoint.coords[0],
                        finalVertex.coords[1] - throughPoint.coords[1],
                    ];

                    // Calculate dot product to determine direction
                    const dotProduct =
                        toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                    // Distance from through point to final vertex
                    const distance = startPt.distanceTo(endPt)[0];

                    // k is positive if same direction, negative if opposite direction
                    k = dotProduct >= 0 ? distance : -distance;
                }

                const construction = this.buildLineSegmentConstruction(combinedName, line, throughPoint, k);
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            }
        } catch (error) {
            console.error('Error in complex line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    protected doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (this.selLogic.selected && Array.isArray(this.selLogic.selected)) {
            const [line, throughPoint, finalSelection] = this.selLogic.selected;

            if (line && throughPoint && !finalSelection) {
                // Second selection: line and point selected, show line preview
                this.selectedLine = line as RenderLine;
                this.selectedPoint = vert(throughPoint as RenderVertex);
                this.createLinePreview(ctrl, this.selectedLine, this.selectedPoint);
            }
        } else if (this.selLogic.selected && !Array.isArray(this.selLogic.selected)) {
            // Only line selected (not array yet)
            const line = this.selLogic.selected as RenderLine;
            if (line && ['RenderLine', 'RenderLineSegment', 'RenderRay'].includes(line.type)) {
                this.selectedLine = line;
            }
        }

        // Always flush at the end
        this.pQ.flush(ctrl);
    }
}
