import { line, Point, point, vector } from '@flatten-js/core';
import {
    BaseBoardViewportManager,
    buildDocumentAwarenessCmdOption,
    CriticalErr,
    DocumentId,
    InferredPointerEvent,
    LocatableEvent,
    mouseLocation,
    NumDPointerChange,
    Position,
    ScreenPosition,
    ViErr,
    ViewportManager,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeoGateway } from '../geo.gateway';
import { ReconstructionHistoryItem, RenameElementHistoryItem, RenderElementHistoryItem } from '../history';
import {
    _angleGeoRenderProp,
    _circleGeoRenderProp,
    _circleShapeGeoRenderProp,
    _ellipseGeoRenderProp,
    _ellipseShapeGeoRenderProp,
    _lineGeoRenderProp,
    _lineSegmentGeoRenderProp,
    _pointGeoRenderProp,
    _polygonGeoRenderProp,
    _sectorGeoRenderProp,
    _sectorShapeGeoRenderProp,
    AngleGeoRenderProp,
    ApplyConstructionResponse,
    CircleGeoRenderProp,
    CircleShapeGeoRenderProp,
    ConstructionRequest,
    DocRenderProp,
    EllipseGeoRenderProp,
    EllipseShapeGeoRenderProp,
    GeoElConstructionRequest,
    GeoRelType,
    GeoRenderElement,
    GeoRenderType,
    LineGeoRenderProp,
    LineSegmentGeoRenderProp,
    ParamSpecs,
    PickNameFunction,
    PointGeoRenderProp,
    PolygonGeoRenderProp,
    RenameElementModel,
    RenderCircle,
    RenderEllipse,
    RenderSector,
    RenderVertex,
    RequestElementName,
    RequireNameForUser,
    SectorGeoRenderProp,
    SectorShapeGeoRenderProp,
    StrokeType,
    ValidationResult,
} from '../model';
import { GeoEpsilon, GeoObjectType, GeoPointerEvent } from '../model/geo.models';

import { EPSILON, GeoDocCtrl, GeoSelectHitContext } from '../objects';
import { GeoRenderer } from '../renderer';
import { SelectedVertex, vert } from '../selectors';
import { constructExec, GeometryTool, validateName } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildPointOnElConstruction } from './point.on.object.tool';
import { buildPointConstruction } from './util.construction';

export function addHistoryItemFromConstructionResponse(
    docCtrl: GeoDocCtrl,
    constructResponse: ApplyConstructionResponse
) {
    try {
        if (constructResponse.render.length < 1) return;
        const historyItem: RenderElementHistoryItem = new RenderElementHistoryItem(docCtrl.editor);
        historyItem.type = 'render-elements';
        historyItem.vm = docCtrl.viewport;
        historyItem.docId = docCtrl.state.id;
        historyItem.renderEls = constructResponse.render;
        historyItem.elIdxes = constructResponse.elIdxes;
        historyItem.ctIndexes = constructResponse.ctIndexes;
        historyItem.renameItems = constructResponse.renameEls;
        docCtrl.editor.addHistoryItem(historyItem);
    } catch (e) {
        if (e instanceof ViErr) throw e;
        throw new CriticalErr('Có lỗi xảy ra khi cập nhật lịch sử thay đổi. Vui lòng tải lại trang', e);
    }
}

export function addRemoveElementHistoryItem(docCtrl: GeoDocCtrl, elIdxes: number[], rels: GeoRenderElement[]) {
    if (elIdxes.length < 1 && rels.length < 1) return;
    const historyItem: RenderElementHistoryItem = new RenderElementHistoryItem(docCtrl.editor);
    historyItem.type = 'remove-elements';
    historyItem.vm = docCtrl.viewport;
    historyItem.docId = docCtrl.state.id;
    historyItem.elIdxes = elIdxes;
    historyItem.renderEls = rels;
    docCtrl.editor.addHistoryItem(historyItem);
}

export function addRenameElementHistoryItem(docCtrl: GeoDocCtrl, renamed: RenameElementModel[]) {
    const historyItem = new RenameElementHistoryItem(docCtrl.editor);
    historyItem.type = 'rename-element';
    historyItem.vm = docCtrl.viewport;
    historyItem.docId = docCtrl.state.id;
    historyItem.renamed = renamed;
    docCtrl.editor.addHistoryItem(historyItem);
}

export function addReconstructionHistoryItem(
    docCtrl: GeoDocCtrl,
    ctIdx: number,
    ctId: string,
    elType: GeoObjectType,
    cgName: string,
    name: string,
    oldParamSpecs: ParamSpecs[],
    newParamSpecs: ParamSpecs[]
) {
    const historyItem: ReconstructionHistoryItem = new ReconstructionHistoryItem(docCtrl.editor);
    historyItem.type = 'reconstruction';
    historyItem.vm = docCtrl.viewport;
    historyItem.docId = docCtrl.state.id;
    historyItem.ctIdx = ctIdx;
    historyItem.ctId = ctId;
    historyItem.elType = elType;
    historyItem.cgName = cgName;
    historyItem.name = name;
    historyItem.oldParamSpecs = oldParamSpecs;
    historyItem.newParamSpecs = newParamSpecs;
    docCtrl.editor.addHistoryItem(historyItem);
}

export function buildPreviewVertexRenderProp(): PointGeoRenderProp {
    const pp = _pointGeoRenderProp();
    pp.pointColor = '#62676B';
    pp.showPointLabel = false;
    return pp;
}

export function buildLineRenderProp(): LineGeoRenderProp {
    const pp = _lineGeoRenderProp();
    pp.lineColor = '#007421';
    pp.lineWeight = 2;
    return pp;
}

export function buildPreviewLineRenderProp(): LineGeoRenderProp {
    const pp = _lineGeoRenderProp();
    pp.lineColor = '#62676B';
    pp.lineWeight = 2;
    pp.strokeStyle = 'Dashed';
    return pp;
}

export function buildLineSegmentRenderProp(): LineSegmentGeoRenderProp {
    const pp = _lineSegmentGeoRenderProp();
    pp.lineColor = '#007421';
    pp.lineWeight = 2;
    return pp;
}

export function buildPreviewLineSegmentRenderProp(): LineSegmentGeoRenderProp {
    const pp = _lineSegmentGeoRenderProp();
    pp.lineColor = '#62676B';
    pp.lineWeight = 2;
    pp.strokeStyle = 'Dashed';
    return pp;
}

export function buildSectorRenderProp(): SectorGeoRenderProp {
    const pp = _sectorGeoRenderProp();
    pp.lineColor = '#007421';
    pp.lineWeight = 2;
    return pp;
}

export function buildPreviewSectorRenderProp(): SectorGeoRenderProp {
    const pp = _sectorGeoRenderProp();
    pp.lineColor = '#62676B';
    pp.lineWeight = 2;
    pp.strokeStyle = 'Dashed';
    return pp;
}

export function buildEllipseRenderProp(): EllipseGeoRenderProp {
    const pp = _ellipseGeoRenderProp();
    pp.lineColor = '#007421';
    pp.lineWeight = 2;
    return pp;
}

export function buildPreviewEllipseRenderProp(): EllipseGeoRenderProp {
    const pp = _ellipseGeoRenderProp();
    pp.lineColor = '#62676B';
    pp.lineWeight = 2;
    pp.strokeStyle = 'Dashed';
    return pp;
}

export function buildPreviewEllipseShapeRenderProp(): EllipseShapeGeoRenderProp {
    const pp = _ellipseShapeGeoRenderProp();
    pp.color = '#62676B';
    pp.opacity = 5;
    pp.lineWeight = 1;
    return pp;
}

export function buildCircleRenderProp(): CircleGeoRenderProp {
    const pp = _circleGeoRenderProp();
    pp.lineColor = '#007421';
    pp.lineWeight = 2;
    return pp;
}

export function buildPreviewCircleRenderProp(): CircleGeoRenderProp {
    const pp = _circleGeoRenderProp();
    pp.lineColor = '#62676B';
    pp.lineWeight = 2;
    pp.strokeStyle = 'Dashed';
    return pp;
}

export function buildAngleRenderProp(): AngleGeoRenderProp {
    const pp = _angleGeoRenderProp();
    pp.color = '#007421';
    pp.opacity = 5;
    return pp;
}

export function buildPreviewAngleRenderProp(): AngleGeoRenderProp {
    const pp = _angleGeoRenderProp();
    pp.color = '#62676B';
    pp.opacity = 5;
    return pp;
}

export function buildPreviewCircleShapeRenderProp(): CircleShapeGeoRenderProp {
    const pp = _circleShapeGeoRenderProp();
    pp.color = '#62676B';
    pp.opacity = 5;
    pp.lineWeight = 1;
    return pp;
}

export function buildSectorShapeRenderProp(): SectorShapeGeoRenderProp {
    const pp = _sectorShapeGeoRenderProp();
    pp.color = '#007421';
    pp.opacity = 5;
    return pp;
}

export function buildPreviewSectorShapeRenderProp(): SectorShapeGeoRenderProp {
    const pp = _sectorShapeGeoRenderProp();
    pp.color = '#62676B';
    pp.opacity = 5;
    return pp;
}

export function buildPolygonRenderProp(): PolygonGeoRenderProp {
    const pp = _polygonGeoRenderProp();
    pp.color = '#00AEEF';
    pp.opacity = 5;
    return pp;
}

export function buildPreviewPolygonRenderProp(): PolygonGeoRenderProp {
    const pp = _polygonGeoRenderProp();
    pp.color = '#62676B';
    pp.opacity = 5;
    return pp;
}

// projectPointOntoLine
export function projectPointOntoLine(point: number[], startPoint: number[], vector: number[]) {
    if (!point) return null;
    // Calculate the dot product of the vector formed by the start point and the point to be projected
    const dotProduct = (point[0] - startPoint[0]) * vector[0] + (point[1] - startPoint[1]) * vector[1];

    // Normalize the vector
    const magnitudeSquared = vector[0] ** 2 + vector[1] ** 2;
    const normalizedVector = [vector[0] / magnitudeSquared, vector[1] / magnitudeSquared];

    // Project the point onto the line
    const projection = [
        startPoint[0] + dotProduct * normalizedVector[0],
        startPoint[1] + dotProduct * normalizedVector[1],
    ];

    return projection;
}

export function isSamePoint(pointerPosInGeo: number[], pointPosInGeo: number[], docCtrl: GeoDocCtrl): boolean {
    if (!pointerPosInGeo || !pointPosInGeo || !docCtrl) return false;
    const pointSize = 5; // to be adjusted to the actual drawn point size
    const unit = docCtrl.state.docRenderProp.screenUnit * docCtrl.state.docRenderProp.scale;
    const epsilon = (2 / unit) * docCtrl.viewport.zoomLevel;
    const e = EPSILON(pointSize, unit, docCtrl.viewport.zoomLevel) + epsilon;
    const d = Math.sqrt((pointPosInGeo[0] - pointerPosInGeo[0]) ** 2 + (pointPosInGeo[1] - pointerPosInGeo[1]) ** 2);

    if (d <= e) return true;
    return false;
}

// unit vector
export function calculateScalingFactor(uVector: number[], sPoint: number[], ePoint: number[]): number {
    // Calculate original length
    const unitVectorLength = Math.sqrt(uVector[0] ** 2 + uVector[1] ** 2);

    // Calculate scaled length
    const scaledLength = Math.sqrt((ePoint[0] - sPoint[0]) ** 2 + (ePoint[1] - sPoint[1]) ** 2);

    // Calculate k
    const k = scaledLength / unitVectorLength;

    return k;
}

export function calculateUnitVector(v: number[]): number[] {
    const magnitude = Math.sqrt(v[0] * v[0] + v[1] * v[1]);

    // Normalize the vector
    const unitVectorX = v[0] / magnitude;
    const unitVectorY = v[1] / magnitude;

    return [unitVectorX, unitVectorY];
}

// ensure that the vector in direction p1 p2 then relIndex p1 must be less than p2 or p1 must be created before p2
export function createVector(p1: RenderVertex, p2: RenderVertex): number[] {
    const isP1Negative = !isValidIdx(p1.relIndex);
    const isP2Negative = !isValidIdx(p2.relIndex);

    // p2 create before p1
    if (isP1Negative && !isP2Negative) {
        const temp = p1;
        p1 = p2;
        p2 = temp;
    }
    // p2 less than p1
    else if (p1.relIndex > p2.relIndex && !isP2Negative && !isP1Negative) {
        const temp = p1;
        p1 = p2;
        p2 = temp;
    }

    return [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1], 0.0];
}

export function handleIfPointerNotInError(
    tool: GeometryTool<any>,
    fn: (...args: any[]) => any,
    ...argv: any[]
): Promise<any> | any {
    try {
        const rs = fn.apply(tool, argv);
        if (rs instanceof Promise)
            return rs.catch(e => {
                if (!(e instanceof GeoPointerNotInError)) throw e;
            });
        return rs;
    } catch (e) {
        if (!(e instanceof GeoPointerNotInError)) throw e;
    }
}

export function geoDocReg(coordStateId: string) {
    return `geoEditor/geoDoc/${coordStateId}`;
}

export function geoLayerReg(coordinatorId: string, docId: number) {
    return `geoEditor/geoLayer/${coordinatorId}/${docId}`;
}

export function geoObjectReg(coordinatorId: string, docId: number) {
    return `geoEditor/geoObject/${coordinatorId}/${docId}`;
}

export function distance2Point(p1: number[], p2: number[]): number {
    if (p1.length === 2 || p2.length === 2) return Math.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2);
    return Math.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2 + (p1[2] - p2[2]) ** 2);
}

export function distance2Position(p1: Position, p2: Position): number {
    return Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2 + (p1.z || 0 - p2.z || 0) ** 2);
}

export const pickPointName: PickNameFunction = (
    docCtrl: GeoDocCtrl,
    excluded: string[] = [],
    previousElementNames?: string[]
): string => {
    const alphabet = [...'ABCDEFGHIJKLMNOPQRSTUVWXYZ'];
    const usedNames = docCtrl.rendererCtrl.usableElements
        .filter(e => e.type === 'RenderVertex' || e.type === 'RenderCircleShape' || e.type == 'RenderCircle')
        .map(e => e.name);

    let postfix = 0;
    let pickName = '';
    let index = 0;

    while (!pickName || usedNames.includes(pickName) || excluded.includes(pickName)) {
        const postfixStr = postfix ? postfix.toString() : '';
        pickName = alphabet[index++] + postfixStr;
        if (index >= alphabet.length) {
            index = 0;
            postfix++;
        }
    }

    if (usedNames.includes(pickName)) {
        return undefined;
    }

    return pickName;
};

export const pickRayName: PickNameFunction = (
    docCtrl: GeoDocCtrl,
    excluded: string[] = [],
    previousElementNames?: string[]
): string => {
    // If we have previous input names, try to use the first point name as base
    if (previousElementNames && previousElementNames.length > 0) {
        const firstPointName = previousElementNames[0];
        if (firstPointName) {
            const usedNames = docCtrl.rendererCtrl.usableElements.filter(e => e.type === 'RenderRay').map(e => e.name);

            // Try base name with 'x' suffix first (e.g., A -> Ax)
            const baseName = firstPointName + 'x';
            if (!excluded.includes(baseName) && !usedNames.includes(baseName)) {
                return baseName;
            }

            // Try with numeric suffixes (e.g., Ax1, Ax2, ...)
            let counter = 1;
            do {
                const name = firstPointName + 'x' + counter;
                if (!excluded.includes(name) && !usedNames.includes(name)) {
                    return name;
                }
                counter++;
            } while (counter < 1000); // Safety limit
        }
    }

    // Fallback to original logic if no existingElementNames or all names are taken
    const alphabet = [...'ABCDEFGHIJKLMNOPQRSTUVWXYZ'];
    const usedNames = docCtrl.rendererCtrl.usableElements.filter(e => e.type === 'RenderRay').map(e => e.name);

    let postfix = 0;
    let index = 0;
    let pickName = '';

    while (!pickName || usedNames.includes(pickName) || excluded.includes(pickName)) {
        const postfixStr = postfix ? postfix.toString() : '';
        pickName = alphabet[index++] + 'x' + postfixStr;
        if (index >= alphabet.length) {
            index = 0;
            postfix++;
        }
    }

    return pickName;
};

export const pickShapeName: PickNameFunction = (
    docCtrl: GeoDocCtrl,
    excluded: string[] = [],
    previousElementNames?: string[]
): string => {
    const alphabet = [...'abcdefghijklmnopqrstuvwxyz'];
    const usedNames = docCtrl.rendererCtrl.usableElements
        .filter(
            e =>
                e.type === 'RenderLine' ||
                e.type === 'RenderVector' ||
                e.type === 'RenderSector' ||
                e.type === 'RenderSectorShape' ||
                e.type === 'RenderEllipse' ||
                e.type === 'RenderEllipseShape' ||
                e.type === 'RenderCircle' ||
                e.type === 'RenderCircleShape' ||
                e.type === 'RenderAngle'
        )
        .map(e => e.name);

    let postfix = 0;
    let index = 0;
    let pickName = '';

    while (!pickName || usedNames.includes(pickName) || excluded.includes(pickName)) {
        const postfixStr = postfix ? postfix.toString() : '';
        pickName = alphabet[index++] + postfixStr;
        if (index >= alphabet.length) {
            index = 0;
            postfix++;
        }
    }

    return pickName;
};

export const pickCircleName: PickNameFunction = (
    docCtrl: GeoDocCtrl,
    exclude: string[] = [],
    previousElementNames?: string[]
): string => {
    const alphabet = ['O'];

    const usedNameCircles = docCtrl.rendererCtrl.usableElements
        .filter(e => e.type === 'RenderCircleShape' || e.type === 'RenderCircle' || e.type === 'RenderVertex')
        .map(e => e.name);

    let postfix = 0;
    let pickName = '';
    let index = 0;

    while (!pickName || usedNameCircles.includes(pickName) || exclude.includes(pickName)) {
        const postfixStr = postfix ? postfix.toString() : '';
        pickName = alphabet[index++] + postfixStr;
        if (index >= alphabet.length) {
            index = 0;
            postfix++;
        }
    }

    return pickName;
};

export function isElementLine(el: GeoRenderElement) {
    return (
        el.type === 'RenderLine' ||
        el.type === 'RenderLineSegment' ||
        el.type === 'RenderRay' ||
        el.type === 'RenderVector'
    );
}

export function isElementCurve(el: GeoRenderElement) {
    return el.type === 'RenderSector' || el.type === 'RenderCircle' || el.type === 'RenderEllipse';
}

export function calculatePosInLayer(pos: Position, docCtrl: GeoDocCtrl): ScreenPosition {
    if (!docCtrl) throw 'doc is undefined';
    if ((docCtrl.editor as GeometryEditor).geoEditorConf.docViewMode === 'bounded') {
        const bd = docCtrl.rendererCtrl.layerState.boundary;

        const tx = Math.min(bd.start.x, bd.end.x);
        const ty = Math.max(bd.start.y, bd.end.y);
        const w = bd.width;
        const h = bd.height;

        const center: Position = { x: tx + w / 2, y: ty - h / 2 }; // center point in board coordinate
        const posInLayer: Position = {
            x: pos.x - center.x,
            y: pos.y - center.y,
        };

        return posInLayer;
    }
    return pos;
}

export function calculateSpacing(docCtrl: GeoDocCtrl, viewport: ViewportManager) {
    const noThinerLine = 5;
    const zoomLevel = calculateZoomLevel(docCtrl, viewport);

    // Adjust grid spacing based on zoom level
    let spacing = noThinerLine;
    const ratioToRescaleSpacing = 1.25;
    while (spacing / zoomLevel / noThinerLine >= ratioToRescaleSpacing) spacing /= 2;
    while (noThinerLine / (spacing / zoomLevel) >= ratioToRescaleSpacing) spacing *= 2;

    if (spacing <= 0.1) spacing = 0.1;
    else if (spacing <= 0.2) spacing = 0.2;
    else if (spacing <= 0.5) spacing = 0.5;
    else spacing = Math.round(spacing);

    return spacing;
}

export function calculateZoomLevel(docCtrl: GeoDocCtrl, viewport: ViewportManager) {
    return ((viewport as BaseBoardViewportManager)?.zoomLevel ?? 1) / (docCtrl.state.docRenderProp?.scale ?? 1);
}

/**
 * Converts a given position to a snapped position based on the snap settings of the given control.
 * If snap to grid is enabled, the position will be snapped to the nearest grid point.
 * If detail grid is enabled, the position will be snapped to a finer grid.
 * Also creates a temporary preview vertex at the snapped position.
 *
 * @param {GeoDocCtrl} ctrl - The control to get the snap settings from
 * @param {Position} position - The position to convert
 * @param {boolean} [stopSnap=false] - Flag indicating whether snap should be stopped
 * @returns {Position} The converted (snapped) position
 * @private
 */
export function convertPointBySnapTool(ctrl: GeoDocCtrl, position: Position, stopSnap = false): Position {
    // Get the viewport and document render properties from the control
    const viewport = ctrl.viewport;
    const drp = ctrl.state.docRenderProp;

    // Destructure the coordinates from the position
    const { x, y, z } = position;

    // If snap mode is disabled or snap to grid is not enabled, or stop snap is true, return the original position
    if (!drp.snapToGrid || stopSnap) {
        return { x, y, z };
    }

    // Calculate the spacing based on the control and viewport
    let spacing: number = calculateSpacing(ctrl, viewport);

    // If spacing is not available, return the original position
    if (!spacing) {
        return { x, y, z };
    }

    // If detail grid is enabled, divide the spacing by 5
    if (drp.detailGrid) {
        spacing = spacing / 5;
    }

    // Calculate the snapped position by rounding the coordinates to the nearest multiple of the spacing
    const snapXAmount = Math.abs(x - +(x / spacing).toFixed() * spacing);
    const snapYAmount = Math.abs(y - +(y / spacing).toFixed() * spacing);
    var pointerPosInGeo;

    // if position is really close to the grid intersection, use the intersection
    if (drp.detailGrid || (snapXAmount < spacing / 5 && snapYAmount < spacing / 5))
        pointerPosInGeo = {
            x: +(x / spacing).toFixed() * spacing,
            y: +(y / spacing).toFixed() * spacing,
            z: z,
        };
    else if (snapXAmount < snapYAmount) {
        // if closer to x, use x snaping
        pointerPosInGeo = {
            x: +(x / spacing).toFixed() * spacing,
            y: y,
            z: z,
        };
    } else
        pointerPosInGeo = {
            x: x,
            y: +(y / spacing).toFixed() * spacing,
            z: z,
        };

    // Create a temporary vertex with the snapped position and other necessary properties
    const vertex: RenderVertex = {
        relIndex: -1111,
        type: 'RenderVertex',
        elType: 'Point',
        name: '',
        renderProp: buildPreviewVertexRenderProp(),
        coords: [pointerPosInGeo.x, pointerPosInGeo.y, 0],
        usable: false,
        valid: false,
        unselectable: true,
    };

    // Return the snapped position
    return pointerPosInGeo;
}

export function validatePointerPos(pos: Position, doc: GeoDocCtrl): boolean {
    if (!doc) return false;
    if ((doc.editor as GeometryEditor).geoEditorConf.docViewMode === 'bounded') {
        const bd = doc.rendererCtrl.layerState.boundary;

        const tx = Math.min(bd.start.x, bd.end.x);
        const ty = Math.max(bd.start.y, bd.end.y);

        const w = bd.width;
        const h = bd.height;

        const bx = tx + w;
        const by = ty - h;

        // check pos inside document boundary or not
        return pos.x > tx && pos.x < bx && pos.y > by && pos.y < ty;
    }
    return true;
}

export function getFocusDocCtrl(editor: GeometryEditor, viewportId: string): GeoDocCtrl | undefined {
    const docCtrls = editor.selectDelegator.getFocusedDocs(viewportId);
    if (!docCtrls || docCtrls.length === 0) {
        return undefined;
    }

    if (docCtrls.length > 1) {
        console.warn('there are multi geo docs focused in viewport ', viewportId);
        return undefined;
    }

    return docCtrls[0];
}

export function mapGetOrSet<K, V>(map: Map<K, V>, k: K, v: V): V {
    const exist = map.get(k);
    if (!exist) {
        map.set(k, v);
        return v;
    }
    return exist;
}

export function renderType(geoType: GeoRelType): GeoRenderType {
    switch (geoType) {
        case 'RenderVertex':
            return 'RenderVertex';
        case 'RenderLineSegment':
        case 'RenderVector':
        case 'RenderRay':
        case 'RenderLine':
            return 'RenderLine';
        case 'RenderSector':
            return 'RenderSector';
        case 'RenderEllipse':
            return 'RenderEllipse';
        case 'RenderCircle':
            return 'RenderCircle';
        case 'RenderAngle':
            return 'RenderAngle';
        default:
            return 'RenderShape';
    }
}

export function isEmpty(value: any): boolean {
    return value === undefined || value == null || (typeof value === 'string' && value.trim().length === 0);
}

export function isValidIdx(idx: any): boolean {
    return !isEmpty(idx) && idx >= 0;
}

export function defaultDocRenderProp(): DocRenderProp {
    const docRenderProp = <DocRenderProp>{
        scale: 1,
        screenUnit: 10,

        // grid
        axis: true,
        grid: true,
        detailGrid: false,

        // snap
        snapMode: false,
        snapToExistingPoints: false,
        snapToGrid: false,

        namingMode: true,

        translation: [0, 0, 0],
        rotation: [0, 0, 0],
    };

    return docRenderProp;
}

/**
 * Utils to get the point and vertex from the pointer event.
 *
 * ! Should be used in the tool classes after checking with `GeometryTool.shouldHandleClick()`.
 * @param tool
 * @param event
 * @param relIndex
 * @param stopSnap
 */
export function getPointAndVertex(
    tool: GeometryTool<any>,
    event: GeoPointerEvent,
    relIndex = -10,
    stopSnap = false
): {
    ctrl: GeoDocCtrl;
    pos: Position;
    hitCtx: GeoSelectHitContext;
    hitEl: GeoRenderElement;
    docGlobalId: DocumentId;
    renderer: GeoRenderer;
    coords: number[];
    vertex: RenderVertex;
} {
    const posAndCtrl = tool.posAndCtrl(event, stopSnap);
    const { ctrl, pos, hitCtx, hitEl } = posAndCtrl;

    if (hitEl) {
        ctrl.editor.selectElement(hitCtx, true);
        const el = hitEl as RenderVertex;
        return {
            ...posAndCtrl,
            coords: el.coords,
            vertex: el,
        };
    } else {
        const v: number[] = [pos.x, pos.y, 0.0];
        const vertex: RenderVertex = {
            relIndex: relIndex,
            type: 'RenderVertex',
            elType: 'Point',
            coords: v,
            renderProp: buildPreviewVertexRenderProp(),
            name: undefined,
            usable: true,
            valid: true,
        };
        return {
            ...posAndCtrl,
            coords: v,
            vertex: vertex,
        };
    }
}

/**
 * Determines if two 2D coordinates are significantly different based on a threshold.
 *
 * @param {number[]} v1 - The first coordinate as an array [x, y].
 * @param {number[]} v2 - The second coordinate as an array [x, y].
 * @param threshold optional threshold to determine if the coordinates are different.
 * @returns {boolean} - Returns `true` if the distance between the coordinates exceeds the threshold, otherwise `false`.
 */
export function isDifferentCoords(v1: number[], v2: number[], threshold: number = GeoEpsilon): boolean {
    if (!v1 || !v2) return false;
    const d = Math.sqrt((v1[0] - v2[0]) ** 2 + (v1[1] - v2[1]) ** 2);
    return d > threshold;
}

/**
 * Default non UI pointer event handler for GeometryTool.
 * It checks if the number of total pointers is greater than 1 and ends the preview mode if necessary.
 *
 * @param tool - The GeometryTool instance.
 * @param event - The pointer event to handle.
 * @returns The modified pointer event.
 */
export function defaultNonUIPointerEventHandler(tool: GeometryTool<any>) {
    return (event: NumDPointerChange | InferredPointerEvent): NumDPointerChange | InferredPointerEvent => {
        if (event.eventType == 'numdpointer') {
            // if the number of total > 1, we should end all current construction process.
            if (event.totalPointer > 1) {
                tool.resetState();
            }
        }

        return event;
    };
}

export function projectOnEl(el: StrokeType, pos: number[], renderer: GeoRenderer): number[] {
    switch (el.type) {
        case 'RenderLine':
        case 'RenderLineSegment':
        case 'RenderRay':
        case 'RenderVector': {
            const v = el.vector;
            const s = el.coord('start', renderer);
            const p = point(pos[0], pos[1]);
            const l = line(point(s[0], s[1]), point(s[0] + v[0], s[1] + v[1]));
            const projection = p.projectionOn(l);
            return [projection.x, projection.y];
        }

        case 'RenderSector':
        case 'RenderCircle': {
            const ar = el as RenderCircle | RenderSector;
            const r = ar.radius;
            const c = ar.coord('center', renderer);
            const cp = point(c[0], c[1]);
            const p = point(pos[0], pos[1]);
            const v = vector(cp, p).normalize().multiply(r);
            return [cp.x + v.x, cp.y + v.y];
        }

        case 'RenderEllipse': {
            const ellipse = el as RenderEllipse;
            const f1 = ellipse.coord('f1', renderer);
            const f2 = ellipse.coord('f2', renderer);

            // Calculate center from f1 and f2
            const center = [(f1[0] + f2[0]) / 2, (f1[1] + f2[1]) / 2];

            // Calculate semi-major and semi-minor axes from ellipse properties
            const a = ellipse.a;
            const b = ellipse.b;

            // Calculate rotation angle from center to f1 (standard convention)
            const rotationAngle = Math.atan2(f1[1] - center[1], f1[0] - center[0]);
            const cosRot = Math.cos(rotationAngle);
            const sinRot = Math.sin(rotationAngle);

            const [cx, cy] = center;

            // Ray-ellipse intersection from center to pointer position
            const [dirX, dirY] = [pos[0] - cx, pos[1] - cy];
            const dirLen = Math.sqrt(dirX * dirX + dirY * dirY);
            if (dirLen < 1e-10) return [cx + a, cy];

            const [normDirX, normDirY] = [dirX / dirLen, dirY / dirLen];
            const [localDirX, localDirY] = [
                normDirX * cosRot + normDirY * sinRot,
                -normDirX * sinRot + normDirY * cosRot,
            ];
            const t = 1 / Math.sqrt((localDirX / a) ** 2 + (localDirY / b) ** 2);
            const [localX, localY] = [t * localDirX, t * localDirY];

            return [cx + localX * cosRot - localY * sinRot, cy + localX * sinRot + localY * cosRot];
        }
    }

    return undefined;
}

/**
 * When represent a point on an object we use a single parametric parameter to represent it
 * - Line / Ray -> multiplication of the parallel vector
 * - LineSegment / Vector -> ratio of distance to start point and end point
 * - Circle / ellipse the rad from the zero angle
 * - Sector the rad from the start vector
 * @param el
 * @param pos
 * @param renderer
 */
export function pointOnElAsParam(el: StrokeType, pos: number[], renderer: GeoRenderer): number {
    switch (el.type) {
        case 'RenderRay':
        case 'RenderLine':
            const v = el.vector;
            const unit = vector(v[0], v[1]).normalize();
            const s = el.coord('start', renderer);
            const p = point(pos[0], pos[1]);
            const vp = vector(point(s[0], s[1]), p);
            if (unit.x != 0 && vp.x != 0) return vp.x / unit.x;
            else return vp.y / unit.y;
        case 'RenderLineSegment':
        case 'RenderVector': {
            const s = el.coord('start', renderer);
            const e = el.coord('end', renderer);
            const p = point(pos[0], pos[1]);

            const sp = vector(point(s[0], s[1]), p);
            const se = vector(point(s[0], s[1]), point(e[0], e[1]));
            if (se.x != 0 && sp.x !== 0) return sp.x / se.x;
            else return sp.y / se.y;
        }
        case 'RenderSector': {
            const sector = el as RenderSector;
            const c = sector.coord('center', renderer);
            const s = sector.coord('start', renderer);
            const cp = point(c[0], c[1]);
            const sp = point(s[0], s[1]);
            const p = point(pos[0], pos[1]);

            // Vector from center to start point
            const vStart = vector(cp, sp).normalize();
            // Vector from center to target point
            const vTarget = vector(cp, p).normalize();

            // Calculate angle from start vector to target vector
            return vStart.angleTo(vTarget);
        }
        case 'RenderCircle': {
            const ar = el as RenderCircle | RenderSector;
            const c = ar.coord('center', renderer);
            const cp = point(c[0], c[1]);
            const p = point(pos[0], pos[1]);
            const v = vector(cp, p).normalize();
            const vx = vector(1, 0);
            return vx.angleTo(v);
        }

        case 'RenderEllipse': {
            const ellipse = el as RenderEllipse;
            const center = ellipse.coord('center', renderer);
            const [cx, cy] = center;
            const f2 = ellipse.coord('f2', renderer);

            // Angle from reference vector (center→f2) to point vector
            const refAngle = Math.atan2(f2[1] - cy, f2[0] - cx);
            const pointAngle = Math.atan2(pos[1] - cy, pos[0] - cx);
            let alpha = pointAngle - refAngle;

            // Normalize to [0, 2π]
            while (alpha < 0) alpha += 2 * Math.PI;
            while (alpha >= 2 * Math.PI) alpha -= 2 * Math.PI;

            return alpha;
        }

        default:
            throw new Error(`Point on ${el.type} is not supported`);
    }
}

export function posInGeo(event: LocatableEvent<any>, doc: GeoDocCtrl, stopSnap: boolean): Position {
    const pointerPos = mouseLocation(event);

    if (!validatePointerPos(pointerPos, doc)) {
        throw new GeoPointerNotInError();
    }

    const pointerPosInLayer = calculatePosInLayer(pointerPos, doc);
    const renderer = doc.rendererCtrl;
    // Calculate the position in the layer and convert it to the geo position based on snap settings
    const pointerPosInGeo = convertPointBySnapTool(doc, renderer.layerToGeoPos(pointerPosInLayer), stopSnap);
    return pointerPosInGeo;
}

/**
 * Given a set of selected vertices, and the targeted object
 * Get names for them. Return the construction requests for points, the list of points in the selection
 * the target object, and the object which the point is on
 * @param selection
 */
export async function assignNames(
    ctrl: GeoDocCtrl,

    selection: (SelectedVertex | RenderVertex)[],
    namingTool: NamingElementTool,

    pointsFor?: string | undefined,
    objName?: string | undefined,
    targetObj?: GeoRenderElement | undefined
): Promise<{
    pcs: GeoElConstructionRequest[] | undefined;
    points: RenderVertex[] | undefined;
    strokes: (StrokeType | undefined)[];
}> {
    const points = selection.map(s => vert(s));
    const strokes = selection.map(s => (Array.isArray(s) && s.length == 2 ? (s[0] as StrokeType) : undefined));
    const constructionPoints = [];
    let shape = [];

    if (targetObj != undefined) {
        if (targetObj.elType == 'Ray') {
            shape = [
                {
                    objName: objName,
                    originElement: [targetObj],
                    pickName: (ctrl: GeoDocCtrl, nameExcluded?: string[], previousElementNames?: string[]) =>
                        pickRayName(ctrl, nameExcluded, previousElementNames),
                    namesToAvoid: [],
                },
            ];
        } else {
            shape = [
                {
                    objName: objName,
                    originElement: [targetObj],
                    pickName: pickShapeName,
                    namesToAvoid: [],
                },
            ];
        }
    }

    const elNameReqs: RequestElementName[] = [
        {
            objName: pointsFor ? pointsFor : 'Điểm',
            originElement: points,
            pickName: pickPointName,
            namesToAvoid: [],
        } as RequestElementName,
    ].concat(shape);
    const inputElNames = await requestElementNames(ctrl, namingTool, elNameReqs);

    const inputPointNames = inputElNames[0];
    if (!inputPointNames || !inputPointNames.length) return { pcs: undefined, points: undefined, strokes: undefined };

    if (targetObj) targetObj.name = inputElNames[1][0];

    for (let i = 0; i < points.length; i++) {
        const p = points[i];
        if (!p.name) {
            p.name = inputPointNames[i];
            const constructionPoint = strokes[i]
                ? buildPointOnElConstruction(p, selection[i][0] as StrokeType, ctrl.rendererCtrl)
                : buildPointConstruction(p.name, {
                      x: p.coords[0],
                      y: p.coords[1],
                  });
            constructionPoints.push(constructionPoint);
        }
    }

    return { pcs: constructionPoints, points: points, strokes: strokes };
}

/**
 * Asynchronously obtains or auto-generates names for a set of geometric elements.
 *
 * @param ctrl - The GeoDocCtrl instance that holds document state and utility methods.
 * @param reqEleNames - An array of RequestElementName objects, each describing
 *                       one or more elements that require naming.
 * @returns A Promise that resolves to a nested array of strings, where each sub-array
 *          contains the final names assigned to the corresponding RequestElementName.
 */
export async function requestElementNames(
    ctrl: GeoDocCtrl,
    namingTool: NamingElementTool,
    reqEleNames: RequestElementName[]
): Promise<string[][]> {
    // Prepare requirements: initial and fallback names for each element
    const requirements: RequireNameForUser[] = reqEleNames.map(req => {
        const originNames = req.originElement.filter(el => !!el).map(el => el.name);

        return {
            type: req.originElement[0]?.type ?? 'RenderVertex',
            objType: req.objName,
            originElNames: originNames,
            inputElNames: [...originNames],
            suggestedNames: [...originNames], // Initialize with original names
        };
    });

    // Auto-generate any missing names using the provided pickName strategies
    // Sequentially fills names so that each new pickName sees previously generated names
    const autoFillNames = (): void => {
        for (let currentReqIndex = 0; currentReqIndex < requirements.length; currentReqIndex++) {
            const currentRequirement = requirements[currentReqIndex];
            const nameValidator = reqEleNames.find(r => r.originElement[0]?.type === currentRequirement.type)!;

            // Fill missing names for each element in the current requirement
            for (let elementIndex = 0; elementIndex < currentRequirement.inputElNames.length; elementIndex++) {
                if (!currentRequirement.inputElNames[elementIndex]) {
                    // Collect names to avoid: already assigned names in current requirement + forbidden names
                    const namesToAvoid = [
                        ...currentRequirement.inputElNames.slice(0, elementIndex),
                        ...nameValidator.namesToAvoid,
                    ];

                    // Get previous requirement's names to use as context for intelligent naming
                    const previousRequirement = requirements[currentReqIndex - 1];
                    const previousElementNames = previousRequirement ? previousRequirement.inputElNames : undefined;

                    // Generate new name using the validator's pickName function
                    currentRequirement.inputElNames[elementIndex] = nameValidator.pickName(
                        ctrl,
                        namesToAvoid,
                        previousElementNames
                    );

                    // Store the auto-generated name as suggested placeholder for UI display
                    currentRequirement.suggestedNames[elementIndex] = currentRequirement.inputElNames[elementIndex];
                }
            }
        }
    };

    // Validation callback for interactive naming
    const validateCallback = (index: number, name: string, type: GeoRelType): ValidationResult => {
        const reqConfig = reqEleNames.find(r => r.originElement[0]?.type === type)!;
        const previousNames = requirements.find(r => r.type === type)!.inputElNames;

        return validateName(ctrl, name, previousNames, reqConfig.namesToAvoid, index, type, reqConfig.onValidate);
    };

    // If interactive mode is enabled, prompt the user for names
    if (!ctrl.state.docRenderProp.namingMode) {
        const result = await namingTool.requireNameFromUser(requirements, validateCallback, autoFillNames);

        if (!result) {
            throw new Error('Unable to obtain naming result');
        }

        switch (result.action) {
            case 'confirm':
                return requirements.map(req => req.inputElNames);

            case 'cancel':
                return [];

            default:
                throw new Error(`Unsupported action: ${result.action}`);
        }
    }

    // If auto-naming mode is active, fill missing names automatically
    autoFillNames();
    return requirements.map(req => req.inputElNames);
}

export async function remoteConstruct(
    ctrl: GeoDocCtrl,
    construction: GeoElConstructionRequest,
    constructionPoints: GeoElConstructionRequest[],
    gateway: GeoGateway,
    displayType?: string
) {
    await ctrl.editor.awarenessFeature.useAwareness(
        ctrl.viewport.id,
        `Đang tạo ${displayType ?? displayType}`,
        buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        async () => {
            const constructResponse = await constructExec(() =>
                gateway.construct(ctrl.state.globalId, [
                    ...constructionPoints.map(
                        c =>
                            <ConstructionRequest>{
                                construction: c,
                            }
                    ),
                    {
                        construction: construction,
                    },
                ])
            );

            await syncRenderCommands(constructResponse.render, ctrl);
            await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
        }
    );
}

export function pointsEqual(p1: Point, p2: Point): boolean {
    return Math.abs(p1.x - p2.x) < GeoEpsilon && Math.abs(p1.y - p2.y) < GeoEpsilon;
}
