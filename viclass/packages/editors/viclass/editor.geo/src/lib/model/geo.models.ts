import {
    BoundaryRectangle,
    DocLocalId,
    DocumentId,
    EditorConfig,
    KeyboardEventData,
    LayerId,
    NativeEventTarget,
    ToolEventData,
    UIPointerEventData,
    VDoc,
    VDocLayer,
} from '@viclass/editor.core';
import { GeoKind } from '@viclass/proto/editor.geo';
import { GeometryToolBar } from '../geo.toolbar';
import { ConstraintTemplateModel, DocRenderProp } from './gateway.models';
import { DefaultGeoRenderProp } from './render.elements.model';

export const GeoEpsilon = Math.pow(10, -8);

export type GeoPointerEvent = UIPointerEventData<NativeEventTarget<any>>;
export type GeoKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;
export type GeoToolEventData = ToolEventData<GeometryToolBar, GeometryToolType>;

export type GeometryToolType =
    | 'CommonPropertiesTool'
    | 'CreateDocumentTool'
    | 'InputCommandTool'
    | 'CreateAngleTool'
    | 'CreatePointTool'
    | 'PointOnObjectTool'
    | 'IntersectionPointTool'
    | 'MiddlePointTool'
    | 'CreateLineSegmentTool'
    | 'CreateLineTool'
    | 'CreateRayTool'
    | 'CreateVectorTool'
    | 'CreateCircleTool'
    | 'CreateEllipseTool'
    | 'CreateSemicircleTool'
    | 'CreateSectorTool'
    | 'CreateTriangleTool'
    | 'CreateIsoscelesTriangleTool'
    | 'CreateEquilateralTriangleTool'
    | 'CreateRightTriangleTool'
    | 'CreateIsoscelesRightTriangleTool'
    | 'CreateSquareTool'
    | 'CreateRectangleTool'
    | 'CreateParallelogramTool'
    | 'CreateTrapezoidTool'
    | 'CreateRhombusTool'
    | 'CreatePolygonTool'
    | 'CreateRegularPolygonTool'
    | 'GeoDocFloatingUITool'
    | 'UpdatePropTool'
    | 'RenameElementTool'
    | 'NamingElementTool'
    | 'CreateAngleByThreePointsTool'
    | 'GeoPanTool'
    | 'GeoZoomTool'
    | 'SettingTool'
    | 'CreatePerpendicularLineTool'
    | 'CreateParallelLineTool'
    | 'CreateBisectorLineTool'
    | 'CreateSymmetricThroughLineTool'
    | 'CreateSymmetricThroughPointTool'
    | 'MoveElementTool'
    | 'ListElementTool';

export type GeoDocInitData = {
    numDim: number;
    kind: GeoKind;
    boundary?: BoundaryRectangle;
    docRenderProp: DocRenderProp;
};

export class GeoDoc implements VDoc {
    layers: GeoLayer[];
    version = 0;

    constructor(
        public globalId: DocumentId,
        public id: DocLocalId,
        public kind: GeoKind = GeoKind.GEO2D,
        public docRenderProp: DocRenderProp,
        public docDefaultElRenderProps: DefaultGeoRenderProp = undefined
    ) {
        this.layers = [];
    }

    getLayers(): VDocLayer[] {
        return this.layers;
    }
    setLayers(layers: VDocLayer[]) {
        this.layers = layers as GeoLayer[];
    }

    addLayer(layer: GeoLayer) {
        this.layers.push(layer);
    }
}

export class GeoLayer implements VDocLayer {
    constructor(
        public id: LayerId,
        public boundary: BoundaryRectangle,
        public zindex: number
    ) {}
    getBoundary(): BoundaryRectangle {
        return this.boundary;
    }
    getZindex() {
        return this.zindex;
    }
}

export class UserConstraintSelection {
    selectedConstraints: UserConstraintInputData[] = [];
}

export interface UserConstraintInputData {
    template: string;
    possibleConstructors: ConstraintTemplateModel[];
    inputs?: UserParamInput[];
    templateFulfill?: string;
}

export interface UserParamInput {
    paramKind: ParamKind;
    input: string;
}

export enum ParamKind {
    'value',
    'name',
    'expression',
}

export const GeoObjectTypeValue = [
    'Angle',
    'Point',
    'Vector',
    'Line',
    'LineSegment',
    'Ray',
    'Circle',
    'Ellipse',
    'Semicircle',
    'CircularSector',
    'Triangle',
    'RightTriangle',
    'IsoscelesTriangle',
    'IsoscelesRightTriangle',
    'EquilateralTriangle',
    'Polygon',
    'RegularPolygon',
    'Quadrilateral',
    'Rectangle',
    'Square',
    'Parallelogram',
    'Rhombus',
    'Trapezoid',
];

export type GeoObjectType =
    | 'Angle'
    | 'Point'
    | 'VectorVi'
    | 'LineVi'
    | 'LineSegment'
    | 'Ray'
    | 'Circle'
    | 'Ellipse'
    | 'Semicircle'
    | 'CircularSector'
    | 'Triangle'
    | 'RightTriangle'
    | 'IsoscelesTriangle'
    | 'IsoscelesRightTriangle'
    | 'EquilateralTriangle'
    | 'Polygon'
    | 'RegularPolygon'
    | 'Quadrilateral'
    | 'Rectangle'
    | 'Square'
    | 'Parallelogram'
    | 'Rhombus'
    | 'Trapezoid';

//TODO: Remove this. GeoEditor doesn't need specific configuration AT THE MOMENT.
export interface GeoEditorConfig extends EditorConfig {
    numDim: number;
    unit: number;
}
