// interface to shorten the use of js reverse routing

import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

// this interface basically match a javascript reverse route generated by the portal.backend
declare global {
    interface ControllerMethod {
        url: string;
        method: string;
        absoluteURL: () => string;
    }

    // Typing for the reverse javascript
    interface ControllerMethodFactory {
        apply: (...params: any[]) => ControllerMethod;
    }

    // RPCInvocation is a function that encapsulate the server API
    interface RPCInvocation<T> {
        (http: HttpClient, options?: any, ...params: any[]): Observable<HttpResponse<T>>;
    }

    interface UploadRPCInvocation<T> {
        (http: HttpClient, formData: FormData, options?: any, ...params: any[]): Observable<HttpResponse<T>>;
    }

    namespace jsRoutes {
        namespace portal.backend.controllers {
            namespace UserController {
                var doLogin: ControllerMethodFactory;
                var profile: ControllerMethodFactory;
                var briefProfiles: ControllerMethodFactory;
                var register: ControllerMethodFactory;
                var checkUserExist: ControllerMethodFactory;
                var doLogout: ControllerMethodFactory;
            }

            namespace LSessionController {
                var createLSession: ControllerMethodFactory;
                var updateLSession: ControllerMethodFactory;
                var uploadSessionAvatar: ControllerMethodFactory;
                var getLSession: ControllerMethodFactory;
                var getClassroomSettings: ControllerMethodFactory;
                var sessionSummaryList: ControllerMethodFactory;
                var approveRegistration: ControllerMethodFactory;
                var rejectRegistration: ControllerMethodFactory;
            }

            namespace LSessionRegistrationController {
                var registerLSession: ControllerMethodFactory;
                var unregisterLSession: ControllerMethodFactory;
                var getRegistrationById: ControllerMethodFactory;
                var getLoggedInRegistration: ControllerMethodFactory;
                var getRegistrationsByLsId: ControllerMethodFactory;
            }

            namespace ClassroomController {
                var loadClassroomActivities: ControllerMethodFactory;
                var getClassroomActivity: ControllerMethodFactory;
            }

            namespace DocumentController {
                var getDocs: ControllerMethodFactory;
            }

            namespace ConfigurationsController {
                var getDataConfigs: ControllerMethodFactory;
            }

            namespace NotificationController {
                var loadNotificationById: ControllerMethodFactory;
                var loadClassroomNotification: ControllerMethodFactory;
            }
        }
    }
}
