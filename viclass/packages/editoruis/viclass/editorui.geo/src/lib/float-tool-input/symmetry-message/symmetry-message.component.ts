import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import {
    GeometryToolBar,
    GeometryToolType,
    SymmetryThroughLineToolState,
    SymmetryThroughPointToolState,
} from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for display current selection message for both CreateSymmetricThroughLineTool and CreateSymmetricThroughPointTool
 */
@Component({
    selector: 'tb-symmetry-message',
    templateUrl: './symmetry-message.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SymmetryMessageComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public currentMessage$ = new BehaviorSubject<string>('');

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    get currentMessage(): string {
        if (this.tooltype === 'CreateSymmetricThroughLineTool') {
            const ts = this.toolbar.toolState(this.tooltype) as SymmetryThroughLineToolState;
            return ts.lineSelected
                ? 'Bạn vui lòng chọn hình đối xứng qua đường thẳng đã chọn'
                : 'Bạn vui lòng chọn đường thẳng trước';
        } else if (this.tooltype === 'CreateSymmetricThroughPointTool') {
            const ts = this.toolbar.toolState(this.tooltype) as SymmetryThroughPointToolState;
            return ts.pointSelected
                ? 'Bạn vui lòng chọn hình đối xứng qua điểm đã chọn'
                : 'Bạn vui lòng chọn điểm đối xứng trước';
        }
        return '';
    }

    updateInputFromToolState() {
        this.currentMessage$.next(this.currentMessage);
    }
}
