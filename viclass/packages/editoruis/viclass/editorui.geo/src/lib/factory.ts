import { ComponentRef, createNgModule, Injector, ViewContainerRef } from '@angular/core';
import { EditorUIComponent } from '@viclass/editorui.loader';
import { GeometrytoolsComponent } from './geometrytools.component';
import { GEOMETRY_UI_SETTINGS, GeometryUiSettings } from './geometrytools.models';
import { GeometryToolsModule } from './geometrytools.module';

export function factory(
    viewContainerRef: ViewContainerRef,
    injector: Injector,
    settings: GeometryUiSettings
): Promise<ComponentRef<EditorUIComponent>> {
    // Create child injector with settings token
    const childInjector = Injector.create({
        providers: [{ provide: GEOMETRY_UI_SETTINGS, useValue: settings }],
        parent: injector,
    });

    const module = createNgModule(GeometryToolsModule, childInjector);
    return new Promise((rs, rj) => {
        rs(
            module.injector.runInContext(() =>
                viewContainerRef.createComponent(GeometrytoolsComponent, {
                    injector: childInjector,
                    ngModuleRef: module,
                })
            )
        );
    });
}
