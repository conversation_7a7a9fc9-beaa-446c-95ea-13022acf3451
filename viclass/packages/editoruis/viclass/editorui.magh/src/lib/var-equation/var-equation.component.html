<div class="flex gap-[5px]">
    <div class="state-btns flex flex-col justify-start">
        <button class="v-tool-btn" disabled>
            <span class="rounded-full w-[14px] h-[14px] border-black border-[1px] bg-white"></span>
        </button>
    </div>
    <div
        class="flex-1 border-[1px] rounded-[12px] overflow-hidden"
        [ngClass]="{
            'bg-orange-100': (invalidInputMethod$ | async),
            '!border-red-500':
                (syntaxError$ | async) ||
                (!equation.isValid && equation.expression !== equation.defaultValue) ||
                equation.keyDuplicated,
        }">
        <div class="flex justify-between items-start p-[5px] min-h-[35px]" (keyup)="$event.stopPropagation()">
            <span class="text-xs pt-2">Biến:</span>
            <div #mathRoot class="block flex-1 overflow-auto"></div>
            <div class="action-btns">
                <button class="v-tool-btn" (click)="deleteEquation()">
                    <span class="vcon vcon-general vcon_delete"></span>
                </button>
            </div>
        </div>
        <div>
            <div
                class="scope-var-slider flex justify-between items-center gap-[16px]"
                *ngIf="!(showEditScopeVar$ | async); else editScopeRange">
                <span class="cursor-pointer py-1 px-3 hover:opacity-50" (click)="showEditScopeVar$.next(true)">{{
                    scopeMin
                }}</span>
                <mat-slider
                    class="flex-1"
                    (input)="changeGlobalScope($event)"
                    [min]="scopeMin"
                    [max]="scopeMax"
                    [step]="equation.globalScope.step">
                    <input matSliderThumb [value]="equation.globalScope.value" />
                </mat-slider>
                <span class="cursor-pointer py-1 px-3 hover:opacity-50" (click)="showEditScopeVar$.next(true)">{{
                    scopeMax
                }}</span>
            </div>
            <ng-template #editScopeRange>
                <div class="edit-scope-range pl-2 pr-1 flex justify-between">
                    <div class="flex justify-start items-center gap-1 p-1">
                        <input
                            type="number"
                            class="max-w-[40px] text-center"
                            [max]="scopeMax"
                            [ngModel]="scopeMin"
                            (ngModelChange)="changeGlobalScopeRange('min', $event)" />
                        <span
                            ><span class="px-2">&le;</span>{{ equation.globalScope.key
                            }}<span class="px-2">&le;</span></span
                        >
                        <input
                            type="number"
                            class="max-w-[40px] text-center"
                            [min]="scopeMin"
                            [ngModel]="scopeMax"
                            (ngModelChange)="changeGlobalScopeRange('max', $event)" />

                        <span class="pl-2"> Bước </span>
                        <input
                            type="number"
                            class="max-w-[40px] text-center"
                            min="0.001"
                            [ngModel]="equation.globalScope.step"
                            (ngModelChange)="changeGlobalScopeRange('step', $event)" />
                    </div>
                    <button class="v-tool-btn" title="Áp dụng" (click)="applyGlobalScopeRange()">
                        <span class="vcon vcon-general vcon_general_yes"></span>
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>

<div *ngIf="invalidInputMethod$ | async" class="text-xs text-orange-500 text-center py-1">
    <span class="vcon vcon-general vcon_general_warning !text-xs">
        <span class="path1"></span>
        <span class="path2"></span>
        <span class="path3"></span>
    </span>
    Vui lòng sử dụng bộ gõ tiếng Anh khi nhập công thức.
</div>
<div *ngIf="syntaxError$ | async as errorMessage" class="text-xs text-red-500 text-center py-1">
    {{ errorMessage }}
</div>
<div
    *ngIf="!(syntaxError$ | async) && !equation.isValid && equation.expression !== equation.defaultValue"
    class="text-xs text-red-500 text-center py-1">
    Biến phải có dạng <i class="font-bold">a = 1</i><br />và không được chứa <i class="font-bold">x</i>,
    <i class="font-bold">y</i>
</div>
<div *ngIf="equation.keyDuplicated && !isEditing" class="text-xs text-red-500 text-center py-1">
    Biến <i class="font-bold">{{ equation.globalScope?.key ?? '' }}</i> đã tồn tại
</div>
