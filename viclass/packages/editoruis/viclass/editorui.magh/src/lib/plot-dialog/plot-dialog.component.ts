import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Inject,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { ComputeEngine } from '@cortex-js/compute-engine';
import { initRandomAwarenessId } from '@viclass/editor.core';
import { EqType, EquationsState, UpdateEquationsTool } from '@viclass/editor.magh';
import { MathfieldElement } from 'lib-mathlive';
import { BehaviorSubject } from 'rxjs';
import { MaghEquationService } from '../magh.equation.service';
import { MATHGRAPH_UI_SETTINGS, MathGraphUiSettings } from '../maghtools.models';

@Component({
    selector: 'app-plot-dialog',
    templateUrl: './plot-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PlotDialogComponent implements OnInit, OnDestroy {
    static engine = new ComputeEngine();

    @Input() equationsState: EquationsState;
    @Input() tool: UpdateEquationsTool;

    @Output() onClose = new EventEmitter<void>();

    get equations() {
        return this.equationsState.equations;
    }

    settingIdx = new BehaviorSubject<number>(-1);

    constructor(
        private maghEquationService: MaghEquationService,
        @Inject(MATHGRAPH_UI_SETTINGS) private settings: MathGraphUiSettings
    ) {}

    private awarenessTeardown: () => void;

    async ngOnInit(): Promise<void> {
        const fontsUri = `${this.settings.mathFieldAssetsRoot}/fonts`;
        const soundsUri = `${this.settings.mathFieldAssetsRoot}/sounds`;
        MathfieldElement.fontsDirectory = fontsUri;
        MathfieldElement.soundsDirectory = soundsUri;
        // @ts-ignore: missing properties expected
        MathfieldElement.computeEngine = PlotDialogComponent.engine;

        const vpId = this.tool.toolbar.viewport.id;
        const awarenessId = await this.tool.editor.awarenessFeature.sendAwarenessCommand(vpId, 'Đang nhập công thức', {
            id: initRandomAwarenessId(),
            type: 'aw-loading',
            useScheduler: true,
            expireAfterSeconds: 5,
            schedulerInterval: 4,
            startAfterSeconds: 0,
            isNotSendToLocalReceiver: true,
        });

        if (awarenessId && vpId) {
            this.awarenessTeardown = () => this.tool.editor.awarenessFeature.clearAwarenessCommand(vpId, awarenessId);
        }
    }

    ngOnDestroy(): void {
        this.awarenessTeardown?.();
    }

    handleToggleSettings(idx: number) {
        this.settingIdx.next(this.settingIdx.value === idx ? -1 : idx);
    }

    handleDeleteEquation(idx: number) {
        if (this.settingIdx.value === idx) {
            this.settingIdx.next(-1);
        }

        this.maghEquationService.deleteEquation(this.tool, idx);
    }

    addPlotEquation() {
        this.tool.addEquation(EqType.Plot);
    }

    addVarEquation() {
        this.tool.addEquation(EqType.ScopeVar);
    }

    addPointEquation() {
        this.tool.addEquation(EqType.Point);
    }
}
