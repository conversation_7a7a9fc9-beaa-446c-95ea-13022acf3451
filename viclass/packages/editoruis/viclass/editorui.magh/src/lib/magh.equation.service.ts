import { Injectable } from '@angular/core';
import { UpdateEquationsTool } from '@viclass/editor.magh';
import { Subject } from 'rxjs';
import {
    EquationStylesChangeEvent,
    UpdateDomainEvent,
    UpdateExpressionEvent,
    UpdateGlobalScopeEvent,
    UpdateInternalScopeEvent,
    UpdateVisibilityEvent,
} from './maghtools.models';

@Injectable({
    providedIn: 'root',
})
export class MaghEquationService {
    readonly equationChanged$ = new Subject<void>();

    updateExpression(tool: UpdateEquationsTool, event: UpdateExpressionEvent) {
        tool.updateExpression(event.index, event.expression, event.finished);
    }

    updateVisibility(tool: UpdateEquationsTool, event: UpdateVisibilityEvent) {
        tool.updateVisibility(event.index, event.hidden);
    }

    deleteEquation(tool: UpdateEquationsTool, idx: number) {
        tool.deleteEquation(idx);
    }

    changeStyle(tool: UpdateEquationsTool, event: EquationStylesChangeEvent) {
        tool.updateStyle(event.index, event.style);
    }

    changeInternalScope(tool: UpdateEquationsTool, event: UpdateInternalScopeEvent) {
        tool.updateInternalScope(event.index, event);
    }

    changeGlobalScope(tool: UpdateEquationsTool, event: UpdateGlobalScopeEvent) {
        tool.updateGlobalScope(event.index, event);
    }

    changeDomain(tool: UpdateEquationsTool, event: UpdateDomainEvent) {
        tool.updateDomain(event.index, event.domain);
    }
}
