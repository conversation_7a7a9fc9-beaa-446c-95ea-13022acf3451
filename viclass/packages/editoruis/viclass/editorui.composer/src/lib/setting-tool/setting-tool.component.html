<div>
    <div class="flex gap-1 items-center">
        <span class="vcon vcon-general vcon_document_composer text-P1"></span>
        <span class="text-[12px] text-P1">CÀI ĐẶT TÀI LIỆU VĂN BẢN</span>
        <div class="flex-grow"></div>
        <button (click)="onClose.emit(true)">
            <span class="vcon vcon-general vcon_delete text-BW1 text-[13px]"></span>
        </button>
    </div>

    <div class="bg-P1 h-[1px]" style="margin: 0.75rem 0"></div>

    <div class="pt-3 overflow-hidden leading-[21px] text-[14px]">
        <div class="overflow-y-auto overflow-x-hidden h-full" *ngIf="!!settings">
            <div class="flex gap-3 flex-col">
                <lib-setting-tool-adjust-number
                    label="Văn bản cách lề"
                    [value]="{ value: settings.padding }"
                    field="padding"
                    [min]="0"
                    [max]="100"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-adjust-number>
            </div>
        </div>
    </div>
</div>
