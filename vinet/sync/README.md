## Description

Syncer service run a WEBRTC server. It receives data from the a peer and synchronize the data to other peers and to backend services (e.g. the Classroom Coordinator Service)

## Configuration

Configuration is explained inside the `conf-sample` folder.

## Development

To run the program during development.

```
go run .
```

A test docker file is provided for testing on remote vps. 

```
cd [folder containing conf folder and source of syncer]

docker build . -f Dockerfile.dev -t viclass/vinet/syncer-dev

docker run --name syncer-dev -p 50000:50000 --mount type=bind,src=$(pwd),target=/viclass/vinet/syncer viclass/vinet/syncer-dev
```
NOTE: currently, need to have --net=host mode in order to make it connectable.

## Deployment

A dockerfile for production is provided. Building and running is quite similar to the development docker. However, it doesn't require the source code in the host system for running, only configuration needed.

**Building**

```
cd [folder containing source of syncer]

docker build . -f Dockerfile.prod -t viclass/vinet/syncer-prod
```

**Running**

```
cd [folder containing conf folder]

docker run --name syncer-prod --net=host -p 50000:50000 --mount type=bind,source=/opt/viclass/service/sync,target=/viclass/vinet/syncer viclass/vinet/syncer-prod
```

NOTE: currently, need to have --net=host mode in order to make it connectable.