:root,
::backdrop {
	/* Colors (dark mode) */
	--sl-color-white: hsl(0, 0%, 100%); /* “white” */
	--sl-color-gray-1: hsl(224, 20%, 94%);
	--sl-color-gray-2: hsl(224, 6%, 77%);
	--sl-color-gray-3: hsl(224, 6%, 56%);
	--sl-color-gray-4: hsl(224, 7%, 36%);
	--sl-color-gray-5: hsl(224, 10%, 23%);
	--sl-color-gray-6: hsl(224, 14%, 16%);
	--sl-color-black: hsl(224, 10%, 10%);

	--sl-hue-orange: 41;
	--sl-color-orange-low: hsl(var(--sl-hue-orange), 39%, 22%);
	--sl-color-orange: hsl(var(--sl-hue-orange), 82%, 63%);
	--sl-color-orange-high: hsl(var(--sl-hue-orange), 82%, 87%);
	--sl-hue-green: 101;
	--sl-color-green-low: hsl(var(--sl-hue-green), 39%, 22%);
	--sl-color-green: hsl(var(--sl-hue-green), 82%, 63%);
	--sl-color-green-high: hsl(var(--sl-hue-green), 82%, 80%);
	--sl-hue-blue: 234;
	--sl-color-blue-low: hsl(var(--sl-hue-blue), 54%, 20%);
	--sl-color-blue: hsl(var(--sl-hue-blue), 100%, 60%);
	--sl-color-blue-high: hsl(var(--sl-hue-blue), 100%, 87%);
	--sl-hue-purple: 281;
	--sl-color-purple-low: hsl(var(--sl-hue-purple), 39%, 22%);
	--sl-color-purple: hsl(var(--sl-hue-purple), 82%, 63%);
	--sl-color-purple-high: hsl(var(--sl-hue-purple), 82%, 89%);
	--sl-hue-red: 339;
	--sl-color-red-low: hsl(var(--sl-hue-red), 39%, 22%);
	--sl-color-red: hsl(var(--sl-hue-red), 82%, 63%);
	--sl-color-red-high: hsl(var(--sl-hue-red), 82%, 87%);

	--sl-color-accent-low: hsl(224, 54%, 20%);
	--sl-color-accent: hsl(224, 100%, 60%);
	--sl-color-accent-high: hsl(224, 100%, 85%);

	--sl-color-text: var(--sl-color-gray-2);
	--sl-color-text-accent: var(--sl-color-accent-high);
	--sl-color-text-invert: var(--sl-color-accent-low);
	--sl-color-bg: var(--sl-color-black);
	--sl-color-bg-nav: var(--sl-color-gray-6);
	--sl-color-bg-sidebar: var(--sl-color-gray-6);
	--sl-color-bg-inline-code: var(--sl-color-gray-5);
	--sl-color-bg-accent: var(--sl-color-accent-high);
	--sl-color-hairline-light: var(--sl-color-gray-5);
	--sl-color-hairline: var(--sl-color-gray-6);
	--sl-color-hairline-shade: var(--sl-color-black);

	--sl-color-backdrop-overlay: hsla(223, 13%, 10%, 0.66);

	/* Shadows (dark mode) */
	--sl-shadow-sm: 0px 1px 1px hsla(0, 0%, 0%, 0.12), 0px 2px 1px hsla(0, 0%, 0%, 0.24);
	--sl-shadow-md: 0px 8px 4px hsla(0, 0%, 0%, 0.08), 0px 5px 2px hsla(0, 0%, 0%, 0.08),
		0px 3px 2px hsla(0, 0%, 0%, 0.12), 0px 1px 1px hsla(0, 0%, 0%, 0.15);
	--sl-shadow-lg: 0px 25px 7px hsla(0, 0%, 0%, 0.03), 0px 16px 6px hsla(0, 0%, 0%, 0.1),
		0px 9px 5px hsla(223, 13%, 10%, 0.33), 0px 4px 4px hsla(0, 0%, 0%, 0.75),
		0px 4px 2px hsla(0, 0%, 0%, 0.25);

	/* Text size and line height */
	--sl-text-2xs: 0.75rem; /* 12px */
	--sl-text-xs: 0.8125rem; /* 13px */
	--sl-text-sm: 0.875rem; /* 14px */
	--sl-text-base: 1rem; /* 16px */
	--sl-text-lg: 1.125rem; /* 18px */
	--sl-text-xl: 1.25rem; /* 20px */
	--sl-text-2xl: 1.5rem; /* 24px */
	--sl-text-3xl: 1.8125rem; /* 29px */
	--sl-text-4xl: 2.1875rem; /* 35px */
	--sl-text-5xl: 2.625rem; /* 42px */
	--sl-text-6xl: 4rem; /* 64px */

	--sl-text-body: var(--sl-text-base);
	--sl-text-body-sm: var(--sl-text-xs);
	--sl-text-code: var(--sl-text-sm);
	--sl-text-code-sm: var(--sl-text-xs);
	--sl-text-h1: var(--sl-text-4xl);
	--sl-text-h2: var(--sl-text-3xl);
	--sl-text-h3: var(--sl-text-2xl);
	--sl-text-h4: var(--sl-text-xl);
	--sl-text-h5: var(--sl-text-lg);

	--sl-line-height: 1.75;
	--sl-line-height-headings: 1.2;

	--sl-font-system: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
		'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
		'Segoe UI Symbol', 'Noto Color Emoji';
	--sl-font-system-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
		'Courier New', monospace;
	--__sl-font: var(--sl-font, var(--sl-font-system)), var(--sl-font-system);
	--__sl-font-mono: var(--sl-font-mono, var(--sl-font-system-mono)), var(--sl-font-system-mono);

	/** Key layout values */
	--sl-nav-height: 3.5rem;
	--sl-nav-pad-x: 1rem;
	--sl-nav-pad-y: 0.75rem;
	--sl-mobile-toc-height: 3rem;
	--sl-sidebar-width: 18.75rem;
	--sl-sidebar-pad-x: 1rem;
	--sl-content-width: 45rem;
	--sl-content-pad-x: 1rem;
	--sl-menu-button-size: 2rem;
	--sl-nav-gap: var(--sl-content-pad-x);
	/* Offset required to show outline inside an element instead of round the outside */
	--sl-outline-offset-inside: -0.1875rem;

	/* Global z-index values */
	--sl-z-index-toc: 4;
	--sl-z-index-menu: 5;
	--sl-z-index-navbar: 10;
	--sl-z-index-skiplink: 20;
}

:root[data-theme='light'],
[data-theme='light'] ::backdrop {
	/* Colours (light mode) */
	--sl-color-white: hsl(224, 10%, 10%);
	--sl-color-gray-1: hsl(224, 14%, 16%);
	--sl-color-gray-2: hsl(224, 10%, 23%);
	--sl-color-gray-3: hsl(224, 7%, 36%);
	--sl-color-gray-4: hsl(224, 6%, 56%);
	--sl-color-gray-5: hsl(224, 6%, 77%);
	--sl-color-gray-6: hsl(224, 20%, 94%);
	--sl-color-gray-7: hsl(224, 19%, 97%);
	--sl-color-black: hsl(0, 0%, 100%);

	--sl-color-orange-high: hsl(var(--sl-hue-orange), 80%, 25%);
	--sl-color-orange: hsl(var(--sl-hue-orange), 90%, 60%);
	--sl-color-orange-low: hsl(var(--sl-hue-orange), 90%, 88%);
	--sl-color-green-high: hsl(var(--sl-hue-green), 80%, 22%);
	--sl-color-green: hsl(var(--sl-hue-green), 90%, 46%);
	--sl-color-green-low: hsl(var(--sl-hue-green), 85%, 90%);
	--sl-color-blue-high: hsl(var(--sl-hue-blue), 80%, 30%);
	--sl-color-blue: hsl(var(--sl-hue-blue), 90%, 60%);
	--sl-color-blue-low: hsl(var(--sl-hue-blue), 88%, 90%);
	--sl-color-purple-high: hsl(var(--sl-hue-purple), 90%, 30%);
	--sl-color-purple: hsl(var(--sl-hue-purple), 90%, 60%);
	--sl-color-purple-low: hsl(var(--sl-hue-purple), 80%, 90%);
	--sl-color-red-high: hsl(var(--sl-hue-red), 80%, 30%);
	--sl-color-red: hsl(var(--sl-hue-red), 90%, 60%);
	--sl-color-red-low: hsl(var(--sl-hue-red), 80%, 90%);

	--sl-color-accent-high: hsl(234, 80%, 30%);
	--sl-color-accent: hsl(234, 90%, 60%);
	--sl-color-accent-low: hsl(234, 88%, 90%);

	--sl-color-text-accent: var(--sl-color-accent);
	--sl-color-text-invert: var(--sl-color-black);
	--sl-color-bg-nav: var(--sl-color-gray-7);
	--sl-color-bg-sidebar: var(--sl-color-bg);
	--sl-color-bg-inline-code: var(--sl-color-gray-6);
	--sl-color-bg-accent: var(--sl-color-accent);
	--sl-color-hairline-light: var(--sl-color-gray-6);
	--sl-color-hairline-shade: var(--sl-color-gray-6);

	--sl-color-backdrop-overlay: hsla(225, 9%, 36%, 0.66);

	/* Shadows (light mode) */
	--sl-shadow-sm: 0px 1px 1px hsla(0, 0%, 0%, 0.06), 0px 2px 1px hsla(0, 0%, 0%, 0.06);
	--sl-shadow-md: 0px 8px 4px hsla(0, 0%, 0%, 0.03), 0px 5px 2px hsla(0, 0%, 0%, 0.03),
		0px 3px 2px hsla(0, 0%, 0%, 0.06), 0px 1px 1px hsla(0, 0%, 0%, 0.06);
	--sl-shadow-lg: 0px 25px 7px rgba(0, 0, 0, 0.01), 0px 16px 6px hsla(0, 0%, 0%, 0.03),
		0px 9px 5px hsla(223, 13%, 10%, 0.08), 0px 4px 4px hsla(0, 0%, 0%, 0.16),
		0px 4px 2px hsla(0, 0%, 0%, 0.04);
}

@media (min-width: 50em) {
	:root {
		--sl-nav-height: 4rem;
		--sl-nav-pad-x: 1.5rem;
		--sl-text-h1: var(--sl-text-5xl);
		--sl-text-h2: var(--sl-text-4xl);
		--sl-text-h3: var(--sl-text-3xl);
		--sl-text-h4: var(--sl-text-2xl);
	}
}

@media (min-width: 72rem) {
	:root {
		--sl-content-pad-x: 1.5rem;
		--sl-mobile-toc-height: 0rem;
	}
}
